  transparent android.R.color  Activity android.app  Application android.app  AppDestinations android.app.Activity  BackHandler android.app.Activity  CoroutineScope android.app.Activity  Dispatchers android.app.Activity  DisposableEffect android.app.Activity  LaunchedEffect android.app.Activity  MainScaffold android.app.Activity  PerformanceMonitor android.app.Activity  
TikoppijTheme android.app.Activity  Toast android.app.Activity  Unit android.app.Activity  WindowCompat android.app.Activity  
WindowManager android.app.Activity  androidx android.app.Activity  backPressedOnce android.app.Activity  currentBackStackEntryAsState android.app.Activity  delay android.app.Activity  enableEdgeToEdge android.app.Activity  finish android.app.Activity  getValue android.app.Activity  isSystemInDarkTheme android.app.Activity  launch android.app.Activity  mutableStateOf android.app.Activity  onCreate android.app.Activity  	onDestroy android.app.Activity  onResume android.app.Activity  provideDelegate android.app.Activity  recordTimePoint android.app.Activity  remember android.app.Activity  rememberNavController android.app.Activity  
setContent android.app.Activity  setValue android.app.Activity  window android.app.Activity  FavoriteRepositoryImpl android.app.Application  HistoryRepositoryImpl android.app.Application  PerformanceMonitor android.app.Application  UserPreferencesRepositoryImpl android.app.Application  favoriteRepository android.app.Application  getInstance android.app.Application  historyRepository android.app.Application  onCreate android.app.Application  recordTimePoint android.app.Application  userPreferencesRepository android.app.Application  Context android.content  AppDestinations android.content.Context  BackHandler android.content.Context  CoroutineScope android.content.Context  Dispatchers android.content.Context  DisposableEffect android.content.Context  FavoriteRepositoryImpl android.content.Context  HistoryRepositoryImpl android.content.Context  LaunchedEffect android.content.Context  MainScaffold android.content.Context  PerformanceMonitor android.content.Context  
TikoppijTheme android.content.Context  Toast android.content.Context  Unit android.content.Context  UserPreferencesRepositoryImpl android.content.Context  WindowCompat android.content.Context  
WindowManager android.content.Context  androidx android.content.Context  applicationContext android.content.Context  backPressedOnce android.content.Context  cacheDir android.content.Context  currentBackStackEntryAsState android.content.Context  	dataStore android.content.Context  delay android.content.Context  enableEdgeToEdge android.content.Context  favoriteDataStore android.content.Context  favoriteRepository android.content.Context  getInstance android.content.Context  getValue android.content.Context  historyDataStore android.content.Context  historyRepository android.content.Context  isSystemInDarkTheme android.content.Context  launch android.content.Context  mutableStateOf android.content.Context  provideDelegate android.content.Context  recordTimePoint android.content.Context  remember android.content.Context  rememberNavController android.content.Context  
setContent android.content.Context  setValue android.content.Context  userPreferencesRepository android.content.Context  window android.content.Context  AppDestinations android.content.ContextWrapper  BackHandler android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  DisposableEffect android.content.ContextWrapper  FavoriteRepositoryImpl android.content.ContextWrapper  HistoryRepositoryImpl android.content.ContextWrapper  LaunchedEffect android.content.ContextWrapper  MainScaffold android.content.ContextWrapper  PerformanceMonitor android.content.ContextWrapper  
TikoppijTheme android.content.ContextWrapper  Toast android.content.ContextWrapper  Unit android.content.ContextWrapper  UserPreferencesRepositoryImpl android.content.ContextWrapper  WindowCompat android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  androidx android.content.ContextWrapper  backPressedOnce android.content.ContextWrapper  currentBackStackEntryAsState android.content.ContextWrapper  delay android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  favoriteRepository android.content.ContextWrapper  getInstance android.content.ContextWrapper  getValue android.content.ContextWrapper  historyRepository android.content.ContextWrapper  isSystemInDarkTheme android.content.ContextWrapper  launch android.content.ContextWrapper  mutableStateOf android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  recordTimePoint android.content.ContextWrapper  remember android.content.ContextWrapper  rememberNavController android.content.ContextWrapper  
setContent android.content.ContextWrapper  setValue android.content.ContextWrapper  userPreferencesRepository android.content.ContextWrapper  window android.content.ContextWrapper  Color android.graphics  TRANSPARENT android.graphics.Color  Uri android.net  toString android.net.Uri  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  Log android.util  d android.util.Log  TextureView android.view  View android.view  
ViewParent android.view  Window android.view  
WindowManager android.view  AppDestinations  android.view.ContextThemeWrapper  BackHandler  android.view.ContextThemeWrapper  CoroutineScope  android.view.ContextThemeWrapper  Dispatchers  android.view.ContextThemeWrapper  DisposableEffect  android.view.ContextThemeWrapper  LaunchedEffect  android.view.ContextThemeWrapper  MainScaffold  android.view.ContextThemeWrapper  PerformanceMonitor  android.view.ContextThemeWrapper  
TikoppijTheme  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  Unit  android.view.ContextThemeWrapper  WindowCompat  android.view.ContextThemeWrapper  
WindowManager  android.view.ContextThemeWrapper  androidx  android.view.ContextThemeWrapper  backPressedOnce  android.view.ContextThemeWrapper  currentBackStackEntryAsState  android.view.ContextThemeWrapper  delay  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  isSystemInDarkTheme  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  mutableStateOf  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  recordTimePoint  android.view.ContextThemeWrapper  remember  android.view.ContextThemeWrapper  rememberNavController  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  setValue  android.view.ContextThemeWrapper  window  android.view.ContextThemeWrapper  MATCH_PARENT android.view.TextureView  Player android.view.TextureView  android android.view.TextureView  apply android.view.TextureView  layoutParams android.view.TextureView  layoutParams android.view.View  parent android.view.View  LayoutParams android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  	javaClass android.view.ViewParent  AndroidGraphicsColor android.view.Window  addFlags android.view.Window  apply android.view.Window  
clearFlags android.view.Window  	decorView android.view.Window  let android.view.Window  navigationBarColor android.view.Window  setBackgroundDrawableResource android.view.Window  setDimAmount android.view.Window  statusBarColor android.view.Window  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  Toast android.widget  LayoutParams android.widget.FrameLayout  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  AppDestinations #androidx.activity.ComponentActivity  BackHandler #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  CoroutineScope #androidx.activity.ComponentActivity  Dispatchers #androidx.activity.ComponentActivity  DisposableEffect #androidx.activity.ComponentActivity  Job #androidx.activity.ComponentActivity  LaunchedEffect #androidx.activity.ComponentActivity  MainScaffold #androidx.activity.ComponentActivity  PerformanceMonitor #androidx.activity.ComponentActivity  
TikoppijTheme #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  Unit #androidx.activity.ComponentActivity  WindowCompat #androidx.activity.ComponentActivity  
WindowManager #androidx.activity.ComponentActivity  androidx #androidx.activity.ComponentActivity  backPressedOnce #androidx.activity.ComponentActivity  currentBackStackEntryAsState #androidx.activity.ComponentActivity  delay #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  isSystemInDarkTheme #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  mutableStateOf #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  onResume #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  recordTimePoint #androidx.activity.ComponentActivity  remember #androidx.activity.ComponentActivity  rememberNavController #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  setValue #androidx.activity.ComponentActivity  window #androidx.activity.ComponentActivity  AppDestinations -androidx.activity.ComponentActivity.Companion  BackHandler -androidx.activity.ComponentActivity.Companion  CoroutineScope -androidx.activity.ComponentActivity.Companion  Dispatchers -androidx.activity.ComponentActivity.Companion  DisposableEffect -androidx.activity.ComponentActivity.Companion  LaunchedEffect -androidx.activity.ComponentActivity.Companion  MainScaffold -androidx.activity.ComponentActivity.Companion  PerformanceMonitor -androidx.activity.ComponentActivity.Companion  
TikoppijTheme -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  Unit -androidx.activity.ComponentActivity.Companion  WindowCompat -androidx.activity.ComponentActivity.Companion  
WindowManager -androidx.activity.ComponentActivity.Companion  androidx -androidx.activity.ComponentActivity.Companion  backPressedOnce -androidx.activity.ComponentActivity.Companion  currentBackStackEntryAsState -androidx.activity.ComponentActivity.Companion  delay -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  isSystemInDarkTheme -androidx.activity.ComponentActivity.Companion  launch -androidx.activity.ComponentActivity.Companion  mutableStateOf -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  recordTimePoint -androidx.activity.ComponentActivity.Companion  remember -androidx.activity.ComponentActivity.Companion  rememberNavController -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  setValue -androidx.activity.ComponentActivity.Companion  window -androidx.activity.ComponentActivity.Companion  BackHandler androidx.activity.compose  
setContent androidx.activity.compose  AnimatedContentScope androidx.compose.animation  AnimatedContentTransitionScope androidx.compose.animation  AnimatedVisibility androidx.compose.animation  AnimatedVisibilityScope androidx.compose.animation  EnterTransition androidx.compose.animation  ExitTransition androidx.compose.animation  fadeIn androidx.compose.animation  fadeOut androidx.compose.animation  AppDestinations /androidx.compose.animation.AnimatedContentScope  CacheManagementScreen /androidx.compose.animation.AnimatedContentScope  FavoriteListScreen /androidx.compose.animation.AnimatedContentScope  HiddenScreen /androidx.compose.animation.AnimatedContentScope  HistoryListScreen /androidx.compose.animation.AnimatedContentScope  
ProfileScreen /androidx.compose.animation.AnimatedContentScope  
ScreenWrapper /androidx.compose.animation.AnimatedContentScope  ToolsScreen /androidx.compose.animation.AnimatedContentScope  VideoPlayerScreen /androidx.compose.animation.AnimatedContentScope  VideoScreen /androidx.compose.animation.AnimatedContentScope  let /androidx.compose.animation.AnimatedContentScope  EnterTransition 9androidx.compose.animation.AnimatedContentTransitionScope  ExitTransition 9androidx.compose.animation.AnimatedContentTransitionScope  BottomNavBar 2androidx.compose.animation.AnimatedVisibilityScope  Column 2androidx.compose.animation.AnimatedVisibilityScope  bottomNavItems 2androidx.compose.animation.AnimatedVisibilityScope  	Companion *androidx.compose.animation.EnterTransition  None *androidx.compose.animation.EnterTransition  None 4androidx.compose.animation.EnterTransition.Companion  	Companion )androidx.compose.animation.ExitTransition  None )androidx.compose.animation.ExitTransition  None 3androidx.compose.animation.ExitTransition.Companion  
AnimationSpec androidx.compose.animation.core  
SpringSpec androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  animateDpAsState androidx.compose.animation.core  animateFloatAsState androidx.compose.animation.core  spring androidx.compose.animation.core  tween androidx.compose.animation.core  ExperimentalFoundationApi androidx.compose.foundation  Image androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  detectDragGestures $androidx.compose.foundation.gestures  detectTapGestures $androidx.compose.foundation.gestures  detectVerticalDragGestures $androidx.compose.foundation.gestures  MutableInteractionSource 'androidx.compose.foundation.interaction  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  BoxWithConstraints "androidx.compose.foundation.layout  BoxWithConstraintsScope "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Date "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  
FavoriteModel "androidx.compose.foundation.layout  FavoriteVideoItem "androidx.compose.foundation.layout  FavoriteViewModel "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  Locale "androidx.compose.foundation.layout  Long "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  R "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TextOverflow "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  UnstableApi "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  aspectRatio "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  
fillMaxHeight "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  formatFavoriteTime "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  offset "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  painterResource "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  statusBarsPadding "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  wrapContentHeight "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceAround .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  AndroidView +androidx.compose.foundation.layout.BoxScope  AppDarkGrey +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  BottomMenuSheet +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  BoxWithConstraints +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  ContentScale +androidx.compose.foundation.layout.BoxScope  DrawerValue +androidx.compose.foundation.layout.BoxScope  FavoriteVideoItem +androidx.compose.foundation.layout.BoxScope  HistoryVideoItem +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  	IntOffset +androidx.compose.foundation.layout.BoxScope  
LazyColumn +androidx.compose.foundation.layout.BoxScope  LocalDensity +androidx.compose.foundation.layout.BoxScope  MATCH_PARENT +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  MutableInteractionSource +androidx.compose.foundation.layout.BoxScope  
PaddingValues +androidx.compose.foundation.layout.BoxScope  Player +androidx.compose.foundation.layout.BoxScope  R +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  SettingItemSwitch +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Surface +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  TextureView +androidx.compose.foundation.layout.BoxScope  Unit +androidx.compose.foundation.layout.BoxScope  
VerticalPager +androidx.compose.foundation.layout.BoxScope  VideoControlButtons +androidx.compose.foundation.layout.BoxScope  VideoDisplayMode +androidx.compose.foundation.layout.BoxScope  VideoPlayerComponent +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  android +androidx.compose.foundation.layout.BoxScope  any +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  aspectRatio +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  	clickable +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  
coerceAtLeast +androidx.compose.foundation.layout.BoxScope  coerceIn +androidx.compose.foundation.layout.BoxScope  detectDragGestures +androidx.compose.foundation.layout.BoxScope  detectTapGestures +androidx.compose.foundation.layout.BoxScope  detectVerticalDragGestures +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  
graphicsLayer +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  itemsIndexed +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  offset +androidx.compose.foundation.layout.BoxScope  onGloballyPositioned +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  painterResource +androidx.compose.foundation.layout.BoxScope  pointerInput +androidx.compose.foundation.layout.BoxScope  remember +androidx.compose.foundation.layout.BoxScope  
roundToInt +androidx.compose.foundation.layout.BoxScope  run +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  stringResource +androidx.compose.foundation.layout.BoxScope  	Alignment :androidx.compose.foundation.layout.BoxWithConstraintsScope  Box :androidx.compose.foundation.layout.BoxWithConstraintsScope  CircleShape :androidx.compose.foundation.layout.BoxWithConstraintsScope  Color :androidx.compose.foundation.layout.BoxWithConstraintsScope  LocalDensity :androidx.compose.foundation.layout.BoxWithConstraintsScope  Modifier :androidx.compose.foundation.layout.BoxWithConstraintsScope  RoundedCornerShape :androidx.compose.foundation.layout.BoxWithConstraintsScope  align :androidx.compose.foundation.layout.BoxWithConstraintsScope  
background :androidx.compose.foundation.layout.BoxWithConstraintsScope  fillMaxWidth :androidx.compose.foundation.layout.BoxWithConstraintsScope  
graphicsLayer :androidx.compose.foundation.layout.BoxWithConstraintsScope  height :androidx.compose.foundation.layout.BoxWithConstraintsScope  maxWidth :androidx.compose.foundation.layout.BoxWithConstraintsScope  run :androidx.compose.foundation.layout.BoxWithConstraintsScope  size :androidx.compose.foundation.layout.BoxWithConstraintsScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  BottomNavBar .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ContentScale .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  HorizontalDivider .androidx.compose.foundation.layout.ColumnScope  HorizontalPager .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Image .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  ListItem .androidx.compose.foundation.layout.ColumnScope  ListItemDefaults .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  MyContentMenuCard .androidx.compose.foundation.layout.ColumnScope  NavBarContentColor .androidx.compose.foundation.layout.ColumnScope  
PaddingValues .androidx.compose.foundation.layout.ColumnScope  R .androidx.compose.foundation.layout.ColumnScope  RadioButton .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  SettingItemSwitch .androidx.compose.foundation.layout.ColumnScope  SettingMenuItem .androidx.compose.foundation.layout.ColumnScope  SettingsMenuCard .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  StatItem .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  TextOverflow .androidx.compose.foundation.layout.ColumnScope  UserInfoCard .androidx.compose.foundation.layout.ColumnScope  VideoControlButton .androidx.compose.foundation.layout.ColumnScope  VideoDisplayMode .androidx.compose.foundation.layout.ColumnScope  
absoluteValue .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  animateFloatAsState .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  bottomNavItems .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  
cardElevation .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  coerceIn .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  formatFavoriteTime .androidx.compose.foundation.layout.ColumnScope  getValue .androidx.compose.foundation.layout.ColumnScope  
graphicsLayer .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  itemsIndexed .androidx.compose.foundation.layout.ColumnScope  lerp .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  painterResource .androidx.compose.foundation.layout.ColumnScope  provideDelegate .androidx.compose.foundation.layout.ColumnScope  repeat .androidx.compose.foundation.layout.ColumnScope  scale .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  stringResource .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  wrapContentHeight .androidx.compose.foundation.layout.ColumnScope  calculateBottomPadding 0androidx.compose.foundation.layout.PaddingValues  calculateLeftPadding 0androidx.compose.foundation.layout.PaddingValues  calculateRightPadding 0androidx.compose.foundation.layout.PaddingValues  calculateTopPadding 0androidx.compose.foundation.layout.PaddingValues  	Alignment +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  MutableInteractionSource +androidx.compose.foundation.layout.RowScope  R +androidx.compose.foundation.layout.RowScope  RadioButton +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  StatItem +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  TextOverflow +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  	clickable +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  
fillMaxHeight +androidx.compose.foundation.layout.RowScope  formatFavoriteTime +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  painterResource +androidx.compose.foundation.layout.RowScope  remember +androidx.compose.foundation.layout.RowScope  repeat +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  example &androidx.compose.foundation.layout.com  tikoppij .androidx.compose.foundation.layout.com.example  model 7androidx.compose.foundation.layout.com.example.tikoppij  
VideoModel =androidx.compose.foundation.layout.com.example.tikoppij.model  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  itemsIndexed  androidx.compose.foundation.lazy  Card .androidx.compose.foundation.lazy.LazyItemScope  CardDefaults .androidx.compose.foundation.lazy.LazyItemScope  Color .androidx.compose.foundation.lazy.LazyItemScope  FavoriteVideoItem .androidx.compose.foundation.lazy.LazyItemScope  HistoryVideoItem .androidx.compose.foundation.lazy.LazyItemScope  ListItem .androidx.compose.foundation.lazy.LazyItemScope  ListItemDefaults .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  animateFloatAsState .androidx.compose.foundation.lazy.LazyItemScope  
cardColors .androidx.compose.foundation.lazy.LazyItemScope  
cardElevation .androidx.compose.foundation.lazy.LazyItemScope  	clickable .androidx.compose.foundation.lazy.LazyItemScope  colors .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  getValue .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  provideDelegate .androidx.compose.foundation.lazy.LazyItemScope  scale .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyListScope  CardDefaults .androidx.compose.foundation.lazy.LazyListScope  Color .androidx.compose.foundation.lazy.LazyListScope  FavoriteVideoItem .androidx.compose.foundation.lazy.LazyListScope  HistoryVideoItem .androidx.compose.foundation.lazy.LazyListScope  ListItem .androidx.compose.foundation.lazy.LazyListScope  ListItemDefaults .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  animateFloatAsState .androidx.compose.foundation.lazy.LazyListScope  
cardColors .androidx.compose.foundation.lazy.LazyListScope  
cardElevation .androidx.compose.foundation.lazy.LazyListScope  	clickable .androidx.compose.foundation.lazy.LazyListScope  colors .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  getValue .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  itemsIndexed .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  provideDelegate .androidx.compose.foundation.lazy.LazyListScope  scale .androidx.compose.foundation.lazy.LazyListScope  HorizontalPager !androidx.compose.foundation.pager  PageInfo !androidx.compose.foundation.pager  PagerLayoutInfo !androidx.compose.foundation.pager  
PagerScope !androidx.compose.foundation.pager  
PagerState !androidx.compose.foundation.pager  
VerticalPager !androidx.compose.foundation.pager  rememberPagerState !androidx.compose.foundation.pager  index *androidx.compose.foundation.pager.PageInfo  visiblePagesInfo 1androidx.compose.foundation.pager.PagerLayoutInfo  	Alignment ,androidx.compose.foundation.pager.PagerScope  Box ,androidx.compose.foundation.pager.PagerScope  Card ,androidx.compose.foundation.pager.PagerScope  CardDefaults ,androidx.compose.foundation.pager.PagerScope  Color ,androidx.compose.foundation.pager.PagerScope  
MaterialTheme ,androidx.compose.foundation.pager.PagerScope  Modifier ,androidx.compose.foundation.pager.PagerScope  Text ,androidx.compose.foundation.pager.PagerScope  	TextAlign ,androidx.compose.foundation.pager.PagerScope  VideoControlButtons ,androidx.compose.foundation.pager.PagerScope  VideoPlayerComponent ,androidx.compose.foundation.pager.PagerScope  
absoluteValue ,androidx.compose.foundation.pager.PagerScope  align ,androidx.compose.foundation.pager.PagerScope  any ,androidx.compose.foundation.pager.PagerScope  
background ,androidx.compose.foundation.pager.PagerScope  
cardColors ,androidx.compose.foundation.pager.PagerScope  coerceIn ,androidx.compose.foundation.pager.PagerScope  dp ,androidx.compose.foundation.pager.PagerScope  fillMaxSize ,androidx.compose.foundation.pager.PagerScope  
graphicsLayer ,androidx.compose.foundation.pager.PagerScope  
isNotEmpty ,androidx.compose.foundation.pager.PagerScope  lerp ,androidx.compose.foundation.pager.PagerScope  padding ,androidx.compose.foundation.pager.PagerScope  animateScrollToPage ,androidx.compose.foundation.pager.PagerState  currentPage ,androidx.compose.foundation.pager.PagerState  currentPageOffsetFraction ,androidx.compose.foundation.pager.PagerState  
layoutInfo ,androidx.compose.foundation.pager.PagerState  	pageCount ,androidx.compose.foundation.pager.PagerState  CircleShape !androidx.compose.foundation.shape  CornerBasedShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  Date androidx.compose.material3  DismissibleDrawerSheet androidx.compose.material3  DismissibleNavigationDrawer androidx.compose.material3  DrawerState androidx.compose.material3  DrawerValue androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
FavoriteModel androidx.compose.material3  FavoriteVideoItem androidx.compose.material3  FavoriteViewModel androidx.compose.material3  
FontWeight androidx.compose.material3  HorizontalDivider androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Int androidx.compose.material3  
LazyColumn androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  List androidx.compose.material3  ListItem androidx.compose.material3  ListItemColors androidx.compose.material3  ListItemDefaults androidx.compose.material3  Locale androidx.compose.material3  Long androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  
PaddingValues androidx.compose.material3  R androidx.compose.material3  RadioButton androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Shapes androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Surface androidx.compose.material3  Switch androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  TextOverflow androidx.compose.material3  	TopAppBar androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  UnstableApi androidx.compose.material3  align androidx.compose.material3  
background androidx.compose.material3  
cardElevation androidx.compose.material3  clip androidx.compose.material3  collectAsState androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  formatFavoriteTime androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  
isNotEmpty androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  painterResource androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberDrawerState androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  secondaryContainer &androidx.compose.material3.ColorScheme  surfaceContainer &androidx.compose.material3.ColorScheme  surfaceContainerLowest &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  tertiaryContainer &androidx.compose.material3.ColorScheme  close &androidx.compose.material3.DrawerState  currentValue &androidx.compose.material3.DrawerState  open &androidx.compose.material3.DrawerState  Closed &androidx.compose.material3.DrawerValue  Open &androidx.compose.material3.DrawerValue  colors +androidx.compose.material3.ListItemDefaults  colorScheme (androidx.compose.material3.MaterialTheme  shapes (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  large !androidx.compose.material3.Shapes  medium !androidx.compose.material3.Shapes  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  displaySmall %androidx.compose.material3.Typography  
headlineLarge %androidx.compose.material3.Typography  
labelSmall %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  example androidx.compose.material3.com  tikoppij &androidx.compose.material3.com.example  model /androidx.compose.material3.com.example.tikoppij  
VideoModel 5androidx.compose.material3.com.example.tikoppij.model  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Box androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  Date androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  
FavoriteModel androidx.compose.runtime  FavoriteVideoItem androidx.compose.runtime  FavoriteViewModel androidx.compose.runtime  
FontWeight androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Int androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  List androidx.compose.runtime  Locale androidx.compose.runtime  Long androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableFloatState androidx.compose.runtime  MutableIntState androidx.compose.runtime  MutableLongState androidx.compose.runtime  MutableState androidx.compose.runtime  OptIn androidx.compose.runtime  
PaddingValues androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  R androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  Text androidx.compose.runtime  
TextButton androidx.compose.runtime  TextOverflow androidx.compose.runtime  	TopAppBar androidx.compose.runtime  Unit androidx.compose.runtime  UnstableApi androidx.compose.runtime  align androidx.compose.runtime  
background androidx.compose.runtime  
cardElevation androidx.compose.runtime  clip androidx.compose.runtime  collectAsState androidx.compose.runtime  com androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  formatFavoriteTime androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  mutableFloatStateOf androidx.compose.runtime  mutableIntStateOf androidx.compose.runtime  mutableLongStateOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  painterResource androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  AndroidGraphicsColor .androidx.compose.runtime.DisposableEffectScope  	Lifecycle .androidx.compose.runtime.DisposableEffectScope  LifecycleEventObserver .androidx.compose.runtime.DisposableEffectScope  PerformanceMonitor .androidx.compose.runtime.DisposableEffectScope  WindowCompat .androidx.compose.runtime.DisposableEffectScope  android .androidx.compose.runtime.DisposableEffectScope  androidx .androidx.compose.runtime.DisposableEffectScope  apply .androidx.compose.runtime.DisposableEffectScope  	javaClass .androidx.compose.runtime.DisposableEffectScope  let .androidx.compose.runtime.DisposableEffectScope  	onDispose .androidx.compose.runtime.DisposableEffectScope  recordTimePoint .androidx.compose.runtime.DisposableEffectScope  setValue *androidx.compose.runtime.MutableFloatState  setValue (androidx.compose.runtime.MutableIntState  setValue )androidx.compose.runtime.MutableLongState  setValue %androidx.compose.runtime.MutableState  value %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  example androidx.compose.runtime.com  tikoppij $androidx.compose.runtime.com.example  model -androidx.compose.runtime.com.example.tikoppij  
VideoModel 3androidx.compose.runtime.com.example.tikoppij.model  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  ComposableFunction3 !androidx.compose.runtime.internal  invoke 5androidx.compose.runtime.internal.ComposableFunction0  rememberSaveable !androidx.compose.runtime.saveable  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  BottomCenter androidx.compose.ui.Alignment  	BottomEnd androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterStart androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  	BottomEnd 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterStart 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  VideoDisplayMode androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  aspectRatio androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  
fillMaxHeight androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  
graphicsLayer androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  invoke androidx.compose.ui.Modifier  offset androidx.compose.ui.Modifier  onGloballyPositioned androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  pointerInput androidx.compose.ui.Modifier  run androidx.compose.ui.Modifier  scale androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  statusBarsPadding androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  wrapContentHeight androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  wrapContentHeight &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  scale androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  x #androidx.compose.ui.geometry.Offset  Color androidx.compose.ui.graphics  GraphicsLayerScope androidx.compose.ui.graphics  
graphicsLayer androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  DarkGray "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  	LightGray "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  DarkGray ,androidx.compose.ui.graphics.Color.Companion  Gray ,androidx.compose.ui.graphics.Color.Companion  	LightGray ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  
absoluteValue /androidx.compose.ui.graphics.GraphicsLayerScope  alpha /androidx.compose.ui.graphics.GraphicsLayerScope  coerceIn /androidx.compose.ui.graphics.GraphicsLayerScope  lerp /androidx.compose.ui.graphics.GraphicsLayerScope  scaleY /androidx.compose.ui.graphics.GraphicsLayerScope  translationX /androidx.compose.ui.graphics.GraphicsLayerScope  Painter $androidx.compose.ui.graphics.painter  ImageVector #androidx.compose.ui.graphics.vector  HapticFeedback "androidx.compose.ui.hapticfeedback  HapticFeedbackType "androidx.compose.ui.hapticfeedback  performHapticFeedback 1androidx.compose.ui.hapticfeedback.HapticFeedback  	Companion 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  	LongPress 5androidx.compose.ui.hapticfeedback.HapticFeedbackType  	LongPress ?androidx.compose.ui.hapticfeedback.HapticFeedbackType.Companion  PointerInputChange !androidx.compose.ui.input.pointer  PointerInputEventHandler !androidx.compose.ui.input.pointer  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  consume 4androidx.compose.ui.input.pointer.PointerInputChange  position 4androidx.compose.ui.input.pointer.PointerInputChange  <SAM-CONSTRUCTOR> :androidx.compose.ui.input.pointer.PointerInputEventHandler  HapticFeedbackType 3androidx.compose.ui.input.pointer.PointerInputScope  
coerceAtLeast 3androidx.compose.ui.input.pointer.PointerInputScope  coerceIn 3androidx.compose.ui.input.pointer.PointerInputScope  detectDragGestures 3androidx.compose.ui.input.pointer.PointerInputScope  detectTapGestures 3androidx.compose.ui.input.pointer.PointerInputScope  detectVerticalDragGestures 3androidx.compose.ui.input.pointer.PointerInputScope  let 3androidx.compose.ui.input.pointer.PointerInputScope  size 3androidx.compose.ui.input.pointer.PointerInputScope  ContentScale androidx.compose.ui.layout  LayoutCoordinates androidx.compose.ui.layout  onGloballyPositioned androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Fit 'androidx.compose.ui.layout.ContentScale  Fit 1androidx.compose.ui.layout.ContentScale.Companion  size ,androidx.compose.ui.layout.LayoutCoordinates  LocalContext androidx.compose.ui.platform  LocalDensity androidx.compose.ui.platform  LocalHapticFeedback androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  painterResource androidx.compose.ui.res  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  copy "androidx.compose.ui.text.TextStyle  fontSize "androidx.compose.ui.text.TextStyle  Font androidx.compose.ui.text.font  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  	Companion +androidx.compose.ui.text.style.TextOverflow  Ellipsis +androidx.compose.ui.text.style.TextOverflow  Ellipsis 5androidx.compose.ui.text.style.TextOverflow.Companion  Density androidx.compose.ui.unit  Dp androidx.compose.ui.unit  	IntOffset androidx.compose.ui.unit  IntSize androidx.compose.ui.unit  LayoutDirection androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  	IntOffset  androidx.compose.ui.unit.Density  
roundToInt  androidx.compose.ui.unit.Density  run  androidx.compose.ui.unit.Density  toPx  androidx.compose.ui.unit.Density  div androidx.compose.ui.unit.Dp  plus androidx.compose.ui.unit.Dp  toPx androidx.compose.ui.unit.Dp  value androidx.compose.ui.unit.Dp  height  androidx.compose.ui.unit.IntSize  width  androidx.compose.ui.unit.IntSize  Ltr (androidx.compose.ui.unit.LayoutDirection  times !androidx.compose.ui.unit.TextUnit  lerp androidx.compose.ui.util  AndroidView androidx.compose.ui.viewinterop  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  AppDestinations #androidx.core.app.ComponentActivity  BackHandler #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  CoroutineScope #androidx.core.app.ComponentActivity  Dispatchers #androidx.core.app.ComponentActivity  DisposableEffect #androidx.core.app.ComponentActivity  Job #androidx.core.app.ComponentActivity  LaunchedEffect #androidx.core.app.ComponentActivity  MainScaffold #androidx.core.app.ComponentActivity  PerformanceMonitor #androidx.core.app.ComponentActivity  
TikoppijTheme #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  Unit #androidx.core.app.ComponentActivity  WindowCompat #androidx.core.app.ComponentActivity  
WindowManager #androidx.core.app.ComponentActivity  androidx #androidx.core.app.ComponentActivity  backPressedOnce #androidx.core.app.ComponentActivity  currentBackStackEntryAsState #androidx.core.app.ComponentActivity  delay #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  isSystemInDarkTheme #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  mutableStateOf #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  recordTimePoint #androidx.core.app.ComponentActivity  remember #androidx.core.app.ComponentActivity  rememberNavController #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  setValue #androidx.core.app.ComponentActivity  window #androidx.core.app.ComponentActivity  toUri androidx.core.net  WindowCompat androidx.core.view  WindowInsetsCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  setDecorFitsSystemWindows androidx.core.view.WindowCompat  
statusBars *androidx.core.view.WindowInsetsCompat.Type  androidx /androidx.core.view.WindowInsetsControllerCompat  apply /androidx.core.view.WindowInsetsControllerCompat  hide /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightNavigationBars /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  show /androidx.core.view.WindowInsetsControllerCompat  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  edit !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  MutablePreferences #androidx.datastore.preferences.core  Preferences #androidx.datastore.preferences.core  booleanPreferencesKey #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  longPreferencesKey #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  set 6androidx.datastore.preferences.core.MutablePreferences  Key /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  AndroidViewModel androidx.lifecycle  	Lifecycle androidx.lifecycle  LifecycleEventObserver androidx.lifecycle  LifecycleOwner androidx.lifecycle  viewModelScope androidx.lifecycle  	onCleared #androidx.lifecycle.AndroidViewModel  Event androidx.lifecycle.Lifecycle  addObserver androidx.lifecycle.Lifecycle  removeObserver androidx.lifecycle.Lifecycle  	Companion "androidx.lifecycle.Lifecycle.Event  ON_PAUSE "androidx.lifecycle.Lifecycle.Event  	ON_RESUME "androidx.lifecycle.Lifecycle.Event  	lifecycle !androidx.lifecycle.LifecycleOwner  	onCleared androidx.lifecycle.ViewModel  LocalLifecycleOwner androidx.lifecycle.compose  	viewModel $androidx.lifecycle.viewmodel.compose  	MediaItem androidx.media3.common  Player androidx.media3.common  Builder  androidx.media3.common.MediaItem  build (androidx.media3.common.MediaItem.Builder  
setMediaId (androidx.media3.common.MediaItem.Builder  setUri (androidx.media3.common.MediaItem.Builder  Listener androidx.media3.common.Player  STATE_ENDED androidx.media3.common.Player  STATE_READY androidx.media3.common.Player  addListener androidx.media3.common.Player  currentPosition androidx.media3.common.Player  duration androidx.media3.common.Player  	isPlaying androidx.media3.common.Player  pause androidx.media3.common.Player  play androidx.media3.common.Player  
playWhenReady androidx.media3.common.Player  
playbackState androidx.media3.common.Player  prepare androidx.media3.common.Player  seekTo androidx.media3.common.Player  setMediaItem androidx.media3.common.Player  setVideoTextureView androidx.media3.common.Player  UnstableApi androidx.media3.common.util  StandaloneDatabaseProvider androidx.media3.database  DefaultDataSource androidx.media3.datasource  Factory ,androidx.media3.datasource.DefaultDataSource  Cache  androidx.media3.datasource.cache  CacheDataSource  androidx.media3.datasource.cache  LeastRecentlyUsedCacheEvictor  androidx.media3.datasource.cache  SimpleCache  androidx.media3.datasource.cache  FLAG_BLOCK_ON_CACHE 0androidx.media3.datasource.cache.CacheDataSource  FLAG_IGNORE_CACHE_ON_ERROR 0androidx.media3.datasource.cache.CacheDataSource  Factory 0androidx.media3.datasource.cache.CacheDataSource  setCache 8androidx.media3.datasource.cache.CacheDataSource.Factory  setFlags 8androidx.media3.datasource.cache.CacheDataSource.Factory  setUpstreamDataSourceFactory 8androidx.media3.datasource.cache.CacheDataSource.Factory  isCached ,androidx.media3.datasource.cache.SimpleCache  keys ,androidx.media3.datasource.cache.SimpleCache  release ,androidx.media3.datasource.cache.SimpleCache  removeResource ,androidx.media3.datasource.cache.SimpleCache  OkHttpDataSource !androidx.media3.datasource.okhttp  Factory 2androidx.media3.datasource.okhttp.OkHttpDataSource  DefaultLoadControl androidx.media3.exoplayer  DefaultRenderersFactory androidx.media3.exoplayer  	ExoPlayer androidx.media3.exoplayer  Builder ,androidx.media3.exoplayer.DefaultLoadControl  build 4androidx.media3.exoplayer.DefaultLoadControl.Builder  setAllocator 4androidx.media3.exoplayer.DefaultLoadControl.Builder  setBufferDurationsMs 4androidx.media3.exoplayer.DefaultLoadControl.Builder  #setPrioritizeTimeOverSizeThresholds 4androidx.media3.exoplayer.DefaultLoadControl.Builder  EXTENSION_RENDERER_MODE_PREFER 1androidx.media3.exoplayer.DefaultRenderersFactory  6experimentalSetEnableMediaCodecVideoRendererPrewarming 1androidx.media3.exoplayer.DefaultRenderersFactory  setExtensionRendererMode 1androidx.media3.exoplayer.DefaultRenderersFactory  Builder #androidx.media3.exoplayer.ExoPlayer  addListener #androidx.media3.exoplayer.ExoPlayer  currentPosition #androidx.media3.exoplayer.ExoPlayer  duration #androidx.media3.exoplayer.ExoPlayer  	isPlaying #androidx.media3.exoplayer.ExoPlayer  let #androidx.media3.exoplayer.ExoPlayer  pause #androidx.media3.exoplayer.ExoPlayer  play #androidx.media3.exoplayer.ExoPlayer  
playWhenReady #androidx.media3.exoplayer.ExoPlayer  
playbackState #androidx.media3.exoplayer.ExoPlayer  prepare #androidx.media3.exoplayer.ExoPlayer  release #androidx.media3.exoplayer.ExoPlayer  seekTo #androidx.media3.exoplayer.ExoPlayer  setMediaItem #androidx.media3.exoplayer.ExoPlayer  setVideoTextureView #androidx.media3.exoplayer.ExoPlayer  build +androidx.media3.exoplayer.ExoPlayer.Builder  setLoadControl +androidx.media3.exoplayer.ExoPlayer.Builder  setMediaSourceFactory +androidx.media3.exoplayer.ExoPlayer.Builder  setReleaseTimeoutMs +androidx.media3.exoplayer.ExoPlayer.Builder  setRenderersFactory +androidx.media3.exoplayer.ExoPlayer.Builder  DefaultMediaSourceFactory  androidx.media3.exoplayer.source  DefaultAllocator "androidx.media3.exoplayer.upstream  NavBackStackEntry androidx.navigation  
NavController androidx.navigation  NavDestination androidx.navigation  NavGraph androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  destination %androidx.navigation.NavBackStackEntry  currentBackStackEntryAsState !androidx.navigation.NavController  graph !androidx.navigation.NavController  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  route "androidx.navigation.NavDestination  startDestinationId androidx.navigation.NavGraph  AppDestinations #androidx.navigation.NavGraphBuilder  CacheManagementScreen #androidx.navigation.NavGraphBuilder  FavoriteListScreen #androidx.navigation.NavGraphBuilder  HiddenScreen #androidx.navigation.NavGraphBuilder  HistoryListScreen #androidx.navigation.NavGraphBuilder  
ProfileScreen #androidx.navigation.NavGraphBuilder  
ScreenWrapper #androidx.navigation.NavGraphBuilder  ToolsScreen #androidx.navigation.NavGraphBuilder  VideoPlayerScreen #androidx.navigation.NavGraphBuilder  VideoScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  let #androidx.navigation.NavGraphBuilder  currentBackStackEntryAsState %androidx.navigation.NavHostController  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  launchSingleTop %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  restoreState %androidx.navigation.NavOptionsBuilder  	saveState "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  Initializer androidx.startup  AppDestinations com.example.tikoppij  Application com.example.tikoppij  BackHandler com.example.tikoppij  Boolean com.example.tikoppij  BottomNavBar com.example.tikoppij  Bundle com.example.tikoppij  Column com.example.tikoppij  ComponentActivity com.example.tikoppij  
Composable com.example.tikoppij  CoroutineScope com.example.tikoppij  Dispatchers com.example.tikoppij  DisposableEffect com.example.tikoppij  ExperimentalMaterial3Api com.example.tikoppij  FavoriteRepository com.example.tikoppij  FavoriteRepositoryImpl com.example.tikoppij  HistoryRepository com.example.tikoppij  HistoryRepositoryImpl com.example.tikoppij  Job com.example.tikoppij  LaunchedEffect com.example.tikoppij  MainActivity com.example.tikoppij  MainScaffold com.example.tikoppij  Modifier com.example.tikoppij  
MyApplication com.example.tikoppij  NavHostController com.example.tikoppij  OptIn com.example.tikoppij  PerformanceMonitor com.example.tikoppij  R com.example.tikoppij  
TikoppijTheme com.example.tikoppij  Toast com.example.tikoppij  Unit com.example.tikoppij  UnstableApi com.example.tikoppij  UserPreferencesRepository com.example.tikoppij  UserPreferencesRepositoryImpl com.example.tikoppij  WindowCompat com.example.tikoppij  
WindowManager com.example.tikoppij  androidx com.example.tikoppij  backPressedOnce com.example.tikoppij  bottomNavItems com.example.tikoppij  currentBackStackEntryAsState com.example.tikoppij  delay com.example.tikoppij  favoriteRepository com.example.tikoppij  fillMaxSize com.example.tikoppij  getInstance com.example.tikoppij  getValue com.example.tikoppij  historyRepository com.example.tikoppij  isSystemInDarkTheme com.example.tikoppij  launch com.example.tikoppij  mutableStateOf com.example.tikoppij  provideDelegate com.example.tikoppij  recordTimePoint com.example.tikoppij  remember com.example.tikoppij  rememberNavController com.example.tikoppij  setValue com.example.tikoppij  userPreferencesRepository com.example.tikoppij  window com.example.tikoppij  AppDestinations !com.example.tikoppij.MainActivity  BackHandler !com.example.tikoppij.MainActivity  CoroutineScope !com.example.tikoppij.MainActivity  Dispatchers !com.example.tikoppij.MainActivity  DisposableEffect !com.example.tikoppij.MainActivity  LaunchedEffect !com.example.tikoppij.MainActivity  MainScaffold !com.example.tikoppij.MainActivity  PerformanceMonitor !com.example.tikoppij.MainActivity  
TikoppijTheme !com.example.tikoppij.MainActivity  Toast !com.example.tikoppij.MainActivity  Unit !com.example.tikoppij.MainActivity  WindowCompat !com.example.tikoppij.MainActivity  
WindowManager !com.example.tikoppij.MainActivity  androidx !com.example.tikoppij.MainActivity  backPressedJob !com.example.tikoppij.MainActivity  backPressedOnce !com.example.tikoppij.MainActivity  currentBackStackEntryAsState !com.example.tikoppij.MainActivity  delay !com.example.tikoppij.MainActivity  enableEdgeToEdge !com.example.tikoppij.MainActivity  finish !com.example.tikoppij.MainActivity  getValue !com.example.tikoppij.MainActivity  isSystemInDarkTheme !com.example.tikoppij.MainActivity  launch !com.example.tikoppij.MainActivity  	mainScope !com.example.tikoppij.MainActivity  mutableStateOf !com.example.tikoppij.MainActivity  provideDelegate !com.example.tikoppij.MainActivity  recordTimePoint !com.example.tikoppij.MainActivity  remember !com.example.tikoppij.MainActivity  rememberNavController !com.example.tikoppij.MainActivity  
setContent !com.example.tikoppij.MainActivity  setValue !com.example.tikoppij.MainActivity  window !com.example.tikoppij.MainActivity  	Companion "com.example.tikoppij.MyApplication  FavoriteRepository "com.example.tikoppij.MyApplication  FavoriteRepositoryImpl "com.example.tikoppij.MyApplication  HistoryRepository "com.example.tikoppij.MyApplication  HistoryRepositoryImpl "com.example.tikoppij.MyApplication  PerformanceMonitor "com.example.tikoppij.MyApplication  UserPreferencesRepository "com.example.tikoppij.MyApplication  UserPreferencesRepositoryImpl "com.example.tikoppij.MyApplication  favoriteRepository "com.example.tikoppij.MyApplication  getInstance "com.example.tikoppij.MyApplication  historyRepository "com.example.tikoppij.MyApplication  recordTimePoint "com.example.tikoppij.MyApplication  userPreferencesRepository "com.example.tikoppij.MyApplication  FavoriteRepositoryImpl ,com.example.tikoppij.MyApplication.Companion  HistoryRepositoryImpl ,com.example.tikoppij.MyApplication.Companion  PerformanceMonitor ,com.example.tikoppij.MyApplication.Companion  UserPreferencesRepositoryImpl ,com.example.tikoppij.MyApplication.Companion  favoriteRepository ,com.example.tikoppij.MyApplication.Companion  getInstance ,com.example.tikoppij.MyApplication.Companion  historyRepository ,com.example.tikoppij.MyApplication.Companion  recordTimePoint ,com.example.tikoppij.MyApplication.Companion  userPreferencesRepository ,com.example.tikoppij.MyApplication.Companion  
ic_arrow_back com.example.tikoppij.R.drawable  ic_arrow_forward com.example.tikoppij.R.drawable  	ic_delete com.example.tikoppij.R.drawable  ic_download com.example.tikoppij.R.drawable  ic_favorite com.example.tikoppij.R.drawable  ic_favorite_border com.example.tikoppij.R.drawable  	ic_hidden com.example.tikoppij.R.drawable  
ic_history com.example.tikoppij.R.drawable  ic_home com.example.tikoppij.R.drawable  ic_menu com.example.tikoppij.R.drawable  ic_pause com.example.tikoppij.R.drawable  ic_play com.example.tikoppij.R.drawable  
ic_play_arrow com.example.tikoppij.R.drawable  
ic_profile com.example.tikoppij.R.drawable  ic_settings com.example.tikoppij.R.drawable  ic_share com.example.tikoppij.R.drawable  ic_tools com.example.tikoppij.R.drawable  
ic_visibility com.example.tikoppij.R.drawable  ic_visibility_off com.example.tikoppij.R.drawable  douyin_sansbold com.example.tikoppij.R.font  auto_play_next_disabled com.example.tikoppij.R.string  auto_play_next_enabled com.example.tikoppij.R.string  auto_play_next_setting com.example.tikoppij.R.string  display_mode_auto_adapt com.example.tikoppij.R.string  display_mode_fit com.example.tikoppij.R.string  display_mode_setting com.example.tikoppij.R.string  download_button com.example.tikoppij.R.string  menu_button com.example.tikoppij.R.string  pause_video com.example.tikoppij.R.string  
play_video com.example.tikoppij.R.string  share_button com.example.tikoppij.R.string  toggle_bottom_nav_bar com.example.tikoppij.R.string  AUTO_PLAY_NEXT_KEY com.example.tikoppij.data  Boolean com.example.tikoppij.data  CacheManager com.example.tikoppij.data  Context com.example.tikoppij.data  	DataStore com.example.tikoppij.data  	Exception com.example.tikoppij.data  FAVORITE_LIST_KEY com.example.tikoppij.data  
FavoriteModel com.example.tikoppij.data  FavoriteRepository com.example.tikoppij.data  FavoriteRepositoryImpl com.example.tikoppij.data  Flow com.example.tikoppij.data  Gson com.example.tikoppij.data  HISTORY_LIST_KEY com.example.tikoppij.data  HistoryModel com.example.tikoppij.data  HistoryRepository com.example.tikoppij.data  HistoryRepositoryImpl com.example.tikoppij.data  IllegalArgumentException com.example.tikoppij.data  List com.example.tikoppij.data  Long com.example.tikoppij.data  MAX_CACHE_SIZE_KEY com.example.tikoppij.data  MAX_HISTORY_COUNT com.example.tikoppij.data  Preferences com.example.tikoppij.data  String com.example.tikoppij.data  System com.example.tikoppij.data  	TypeToken com.example.tikoppij.data  UnstableApi com.example.tikoppij.data  UserPreferencesRepository com.example.tikoppij.data  UserPreferencesRepositoryImpl com.example.tikoppij.data  VIDEO_DISPLAY_MODE_KEY com.example.tikoppij.data  VideoDisplayMode com.example.tikoppij.data  
VideoModel com.example.tikoppij.data  Volatile com.example.tikoppij.data  also com.example.tikoppij.data  any com.example.tikoppij.data  booleanPreferencesKey com.example.tikoppij.data  coerceIn com.example.tikoppij.data  	dataStore com.example.tikoppij.data  edit com.example.tikoppij.data  	emptyList com.example.tikoppij.data  filter com.example.tikoppij.data  listOf com.example.tikoppij.data  longPreferencesKey com.example.tikoppij.data  map com.example.tikoppij.data  plus com.example.tikoppij.data  preferencesDataStore com.example.tikoppij.data  provideDelegate com.example.tikoppij.data  stringPreferencesKey com.example.tikoppij.data  synchronized com.example.tikoppij.data  take com.example.tikoppij.data  addFavorite ,com.example.tikoppij.data.FavoriteRepository  clearFavorites ,com.example.tikoppij.data.FavoriteRepository  getFavoriteList ,com.example.tikoppij.data.FavoriteRepository  
isFavorite ,com.example.tikoppij.data.FavoriteRepository  removeFavorite ,com.example.tikoppij.data.FavoriteRepository  Boolean 0com.example.tikoppij.data.FavoriteRepositoryImpl  	Companion 0com.example.tikoppij.data.FavoriteRepositoryImpl  Context 0com.example.tikoppij.data.FavoriteRepositoryImpl  	DataStore 0com.example.tikoppij.data.FavoriteRepositoryImpl  	Exception 0com.example.tikoppij.data.FavoriteRepositoryImpl  FAVORITE_LIST_KEY 0com.example.tikoppij.data.FavoriteRepositoryImpl  
FavoriteModel 0com.example.tikoppij.data.FavoriteRepositoryImpl  FavoriteRepositoryImpl 0com.example.tikoppij.data.FavoriteRepositoryImpl  Flow 0com.example.tikoppij.data.FavoriteRepositoryImpl  Gson 0com.example.tikoppij.data.FavoriteRepositoryImpl  INSTANCE 0com.example.tikoppij.data.FavoriteRepositoryImpl  List 0com.example.tikoppij.data.FavoriteRepositoryImpl  Preferences 0com.example.tikoppij.data.FavoriteRepositoryImpl  String 0com.example.tikoppij.data.FavoriteRepositoryImpl  System 0com.example.tikoppij.data.FavoriteRepositoryImpl  	TypeToken 0com.example.tikoppij.data.FavoriteRepositoryImpl  
VideoModel 0com.example.tikoppij.data.FavoriteRepositoryImpl  Volatile 0com.example.tikoppij.data.FavoriteRepositoryImpl  also 0com.example.tikoppij.data.FavoriteRepositoryImpl  any 0com.example.tikoppij.data.FavoriteRepositoryImpl  context 0com.example.tikoppij.data.FavoriteRepositoryImpl  edit 0com.example.tikoppij.data.FavoriteRepositoryImpl  	emptyList 0com.example.tikoppij.data.FavoriteRepositoryImpl  favoriteDataStore 0com.example.tikoppij.data.FavoriteRepositoryImpl  favoriteListType 0com.example.tikoppij.data.FavoriteRepositoryImpl  filter 0com.example.tikoppij.data.FavoriteRepositoryImpl  getFavoriteList 0com.example.tikoppij.data.FavoriteRepositoryImpl  getFavoriteListSync 0com.example.tikoppij.data.FavoriteRepositoryImpl  getInstance 0com.example.tikoppij.data.FavoriteRepositoryImpl  gson 0com.example.tikoppij.data.FavoriteRepositoryImpl  map 0com.example.tikoppij.data.FavoriteRepositoryImpl  plus 0com.example.tikoppij.data.FavoriteRepositoryImpl  preferencesDataStore 0com.example.tikoppij.data.FavoriteRepositoryImpl  provideDelegate 0com.example.tikoppij.data.FavoriteRepositoryImpl  stringPreferencesKey 0com.example.tikoppij.data.FavoriteRepositoryImpl  synchronized 0com.example.tikoppij.data.FavoriteRepositoryImpl  FAVORITE_LIST_KEY :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  
FavoriteModel :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  FavoriteRepositoryImpl :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  Gson :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  INSTANCE :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  System :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  also :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  any :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  edit :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  	emptyList :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  favoriteDataStore :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  filter :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  getInstance :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  map :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  plus :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  preferencesDataStore :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  provideDelegate :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  stringPreferencesKey :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  synchronized :com.example.tikoppij.data.FavoriteRepositoryImpl.Companion  
addHistory +com.example.tikoppij.data.HistoryRepository  clearHistory +com.example.tikoppij.data.HistoryRepository  getHistoryList +com.example.tikoppij.data.HistoryRepository  
removeHistory +com.example.tikoppij.data.HistoryRepository  updateWatchProgress +com.example.tikoppij.data.HistoryRepository  	Companion /com.example.tikoppij.data.HistoryRepositoryImpl  Context /com.example.tikoppij.data.HistoryRepositoryImpl  	DataStore /com.example.tikoppij.data.HistoryRepositoryImpl  	Exception /com.example.tikoppij.data.HistoryRepositoryImpl  Flow /com.example.tikoppij.data.HistoryRepositoryImpl  Gson /com.example.tikoppij.data.HistoryRepositoryImpl  HISTORY_LIST_KEY /com.example.tikoppij.data.HistoryRepositoryImpl  HistoryModel /com.example.tikoppij.data.HistoryRepositoryImpl  HistoryRepositoryImpl /com.example.tikoppij.data.HistoryRepositoryImpl  INSTANCE /com.example.tikoppij.data.HistoryRepositoryImpl  List /com.example.tikoppij.data.HistoryRepositoryImpl  Long /com.example.tikoppij.data.HistoryRepositoryImpl  MAX_HISTORY_COUNT /com.example.tikoppij.data.HistoryRepositoryImpl  Preferences /com.example.tikoppij.data.HistoryRepositoryImpl  String /com.example.tikoppij.data.HistoryRepositoryImpl  System /com.example.tikoppij.data.HistoryRepositoryImpl  	TypeToken /com.example.tikoppij.data.HistoryRepositoryImpl  
VideoModel /com.example.tikoppij.data.HistoryRepositoryImpl  Volatile /com.example.tikoppij.data.HistoryRepositoryImpl  also /com.example.tikoppij.data.HistoryRepositoryImpl  context /com.example.tikoppij.data.HistoryRepositoryImpl  edit /com.example.tikoppij.data.HistoryRepositoryImpl  	emptyList /com.example.tikoppij.data.HistoryRepositoryImpl  filter /com.example.tikoppij.data.HistoryRepositoryImpl  getHistoryListSync /com.example.tikoppij.data.HistoryRepositoryImpl  getInstance /com.example.tikoppij.data.HistoryRepositoryImpl  gson /com.example.tikoppij.data.HistoryRepositoryImpl  historyDataStore /com.example.tikoppij.data.HistoryRepositoryImpl  historyListType /com.example.tikoppij.data.HistoryRepositoryImpl  listOf /com.example.tikoppij.data.HistoryRepositoryImpl  map /com.example.tikoppij.data.HistoryRepositoryImpl  plus /com.example.tikoppij.data.HistoryRepositoryImpl  preferencesDataStore /com.example.tikoppij.data.HistoryRepositoryImpl  provideDelegate /com.example.tikoppij.data.HistoryRepositoryImpl  stringPreferencesKey /com.example.tikoppij.data.HistoryRepositoryImpl  synchronized /com.example.tikoppij.data.HistoryRepositoryImpl  take /com.example.tikoppij.data.HistoryRepositoryImpl  Gson 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  HISTORY_LIST_KEY 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  HistoryModel 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  HistoryRepositoryImpl 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  INSTANCE 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  MAX_HISTORY_COUNT 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  System 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  also 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  edit 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  	emptyList 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  filter 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  getInstance 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  historyDataStore 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  listOf 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  map 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  plus 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  preferencesDataStore 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  provideDelegate 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  stringPreferencesKey 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  synchronized 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  take 9com.example.tikoppij.data.HistoryRepositoryImpl.Companion  autoPlayNextEnabled 3com.example.tikoppij.data.UserPreferencesRepository  maxCacheSize 3com.example.tikoppij.data.UserPreferencesRepository  updateAutoPlayNextEnabled 3com.example.tikoppij.data.UserPreferencesRepository  updateMaxCacheSize 3com.example.tikoppij.data.UserPreferencesRepository  updateVideoDisplayMode 3com.example.tikoppij.data.UserPreferencesRepository  videoDisplayMode 3com.example.tikoppij.data.UserPreferencesRepository  AUTO_PLAY_NEXT_KEY 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  Boolean 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  CacheManager 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  	Companion 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  Context 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  Flow 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  INSTANCE 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  IllegalArgumentException 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  Long 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  MAX_CACHE_SIZE_KEY 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  UserPreferencesRepository 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  UserPreferencesRepositoryImpl 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  VIDEO_DISPLAY_MODE_KEY 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  VideoDisplayMode 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  Volatile 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  booleanPreferencesKey 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  coerceIn 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  context 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  	dataStore 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  edit 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  getInstance 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  longPreferencesKey 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  map 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  stringPreferencesKey 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  synchronized 7com.example.tikoppij.data.UserPreferencesRepositoryImpl  AUTO_PLAY_NEXT_KEY Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  CacheManager Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  INSTANCE Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  MAX_CACHE_SIZE_KEY Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  UserPreferencesRepositoryImpl Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  VIDEO_DISPLAY_MODE_KEY Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  VideoDisplayMode Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  booleanPreferencesKey Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  coerceIn Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  	dataStore Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  edit Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  getInstance Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  longPreferencesKey Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  map Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  stringPreferencesKey Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  synchronized Acom.example.tikoppij.data.UserPreferencesRepositoryImpl.Companion  AppInitializer  com.example.tikoppij.initializer  Class  com.example.tikoppij.initializer  Context  com.example.tikoppij.initializer  Initializer  com.example.tikoppij.initializer  List  com.example.tikoppij.initializer  MediaPlayerService  com.example.tikoppij.initializer  NetworkProvider  com.example.tikoppij.initializer  PerformanceMonitor  com.example.tikoppij.initializer  Unit  com.example.tikoppij.initializer  UnstableApi  com.example.tikoppij.initializer  Volatile  com.example.tikoppij.initializer  also  com.example.tikoppij.initializer  	emptyList  com.example.tikoppij.initializer  getMediaPlayerService  com.example.tikoppij.initializer  
initialize  com.example.tikoppij.initializer  recordTimePoint  com.example.tikoppij.initializer  synchronized  com.example.tikoppij.initializer  Class /com.example.tikoppij.initializer.AppInitializer  	Companion /com.example.tikoppij.initializer.AppInitializer  Context /com.example.tikoppij.initializer.AppInitializer  Initializer /com.example.tikoppij.initializer.AppInitializer  List /com.example.tikoppij.initializer.AppInitializer  MediaPlayerService /com.example.tikoppij.initializer.AppInitializer  NetworkProvider /com.example.tikoppij.initializer.AppInitializer  PerformanceMonitor /com.example.tikoppij.initializer.AppInitializer  Volatile /com.example.tikoppij.initializer.AppInitializer  also /com.example.tikoppij.initializer.AppInitializer  	emptyList /com.example.tikoppij.initializer.AppInitializer  getMediaPlayerService /com.example.tikoppij.initializer.AppInitializer  
initialize /com.example.tikoppij.initializer.AppInitializer  mediaPlayerServiceInstance /com.example.tikoppij.initializer.AppInitializer  recordTimePoint /com.example.tikoppij.initializer.AppInitializer  synchronized /com.example.tikoppij.initializer.AppInitializer  MediaPlayerService 9com.example.tikoppij.initializer.AppInitializer.Companion  NetworkProvider 9com.example.tikoppij.initializer.AppInitializer.Companion  PerformanceMonitor 9com.example.tikoppij.initializer.AppInitializer.Companion  also 9com.example.tikoppij.initializer.AppInitializer.Companion  	emptyList 9com.example.tikoppij.initializer.AppInitializer.Companion  getMediaPlayerService 9com.example.tikoppij.initializer.AppInitializer.Companion  
initialize 9com.example.tikoppij.initializer.AppInitializer.Companion  mediaPlayerServiceInstance 9com.example.tikoppij.initializer.AppInitializer.Companion  recordTimePoint 9com.example.tikoppij.initializer.AppInitializer.Companion  synchronized 9com.example.tikoppij.initializer.AppInitializer.Companion  
FavoriteModel com.example.tikoppij.model  Float com.example.tikoppij.model  HistoryModel com.example.tikoppij.model  Int com.example.tikoppij.model  List com.example.tikoppij.model  Long com.example.tikoppij.model  String com.example.tikoppij.model  
VideoListData com.example.tikoppij.model  
VideoModel com.example.tikoppij.model  
VideoResponse com.example.tikoppij.model  
VideoModel (com.example.tikoppij.model.FavoriteModel  category (com.example.tikoppij.model.FavoriteModel  favoriteTime (com.example.tikoppij.model.FavoriteModel  height (com.example.tikoppij.model.FavoriteModel  toVideoModel (com.example.tikoppij.model.FavoriteModel  url (com.example.tikoppij.model.FavoriteModel  videoId (com.example.tikoppij.model.FavoriteModel  width (com.example.tikoppij.model.FavoriteModel  
VideoModel 'com.example.tikoppij.model.HistoryModel  category 'com.example.tikoppij.model.HistoryModel  copy 'com.example.tikoppij.model.HistoryModel  height 'com.example.tikoppij.model.HistoryModel  toVideoModel 'com.example.tikoppij.model.HistoryModel  url 'com.example.tikoppij.model.HistoryModel  videoId 'com.example.tikoppij.model.HistoryModel  
watchProgress 'com.example.tikoppij.model.HistoryModel  	watchTime 'com.example.tikoppij.model.HistoryModel  width 'com.example.tikoppij.model.HistoryModel  list (com.example.tikoppij.model.VideoListData  Category %com.example.tikoppij.model.VideoModel  getAspectRatio %com.example.tikoppij.model.VideoModel  height %com.example.tikoppij.model.VideoModel  url %com.example.tikoppij.model.VideoModel  video_id %com.example.tikoppij.model.VideoModel  width %com.example.tikoppij.model.VideoModel  data (com.example.tikoppij.model.VideoResponse  
ApiService com.example.tikoppij.network  GET com.example.tikoppij.network  GsonConverterFactory com.example.tikoppij.network  HttpLoggingInterceptor com.example.tikoppij.network  Int com.example.tikoppij.network  NetworkProvider com.example.tikoppij.network  OkHttpClient com.example.tikoppij.network  Query com.example.tikoppij.network  Response com.example.tikoppij.network  Retrofit com.example.tikoppij.network  TimeUnit com.example.tikoppij.network  
VideoResponse com.example.tikoppij.network  apply com.example.tikoppij.network  getValue com.example.tikoppij.network  java com.example.tikoppij.network  lazy com.example.tikoppij.network  provideDelegate com.example.tikoppij.network  Level 3com.example.tikoppij.network.HttpLoggingInterceptor  
ApiService ,com.example.tikoppij.network.NetworkProvider  BASE_URL ,com.example.tikoppij.network.NetworkProvider  GET ,com.example.tikoppij.network.NetworkProvider  GsonConverterFactory ,com.example.tikoppij.network.NetworkProvider  HttpLoggingInterceptor ,com.example.tikoppij.network.NetworkProvider  Int ,com.example.tikoppij.network.NetworkProvider  OkHttpClient ,com.example.tikoppij.network.NetworkProvider  Query ,com.example.tikoppij.network.NetworkProvider  Response ,com.example.tikoppij.network.NetworkProvider  Retrofit ,com.example.tikoppij.network.NetworkProvider  TimeUnit ,com.example.tikoppij.network.NetworkProvider  
VideoResponse ,com.example.tikoppij.network.NetworkProvider  
apiHttpClient ,com.example.tikoppij.network.NetworkProvider  
apiService ,com.example.tikoppij.network.NetworkProvider  apply ,com.example.tikoppij.network.NetworkProvider  createApiHttpClient ,com.example.tikoppij.network.NetworkProvider  createBaseHttpClientBuilder ,com.example.tikoppij.network.NetworkProvider  createRetrofit ,com.example.tikoppij.network.NetworkProvider  createVideoHttpClient ,com.example.tikoppij.network.NetworkProvider  getValue ,com.example.tikoppij.network.NetworkProvider  java ,com.example.tikoppij.network.NetworkProvider  lazy ,com.example.tikoppij.network.NetworkProvider  provideDelegate ,com.example.tikoppij.network.NetworkProvider  videoHttpClient ,com.example.tikoppij.network.NetworkProvider  getVideoList 7com.example.tikoppij.network.NetworkProvider.ApiService  Level Ccom.example.tikoppij.network.NetworkProvider.HttpLoggingInterceptor  Builder 9com.example.tikoppij.network.NetworkProvider.OkHttpClient  Builder )com.example.tikoppij.network.OkHttpClient  	Alignment "com.example.tikoppij.ui.components  AndroidGraphicsColor "com.example.tikoppij.ui.components  AndroidView "com.example.tikoppij.ui.components  
AnimationSpec "com.example.tikoppij.ui.components  AppDarkGrey "com.example.tikoppij.ui.components  Arrangement "com.example.tikoppij.ui.components  Boolean "com.example.tikoppij.ui.components  BottomMenuSheet "com.example.tikoppij.ui.components  BottomNavBar "com.example.tikoppij.ui.components  
BottomNavItem "com.example.tikoppij.ui.components  Box "com.example.tikoppij.ui.components  BoxWithConstraints "com.example.tikoppij.ui.components  CircleShape "com.example.tikoppij.ui.components  Color "com.example.tikoppij.ui.components  Column "com.example.tikoppij.ui.components  
Composable "com.example.tikoppij.ui.components  DefaultDrawerContent "com.example.tikoppij.ui.components  DrawerValue "com.example.tikoppij.ui.components  ExperimentalMaterial3Api "com.example.tikoppij.ui.components  Float "com.example.tikoppij.ui.components  HapticFeedbackType "com.example.tikoppij.ui.components  Icon "com.example.tikoppij.ui.components  Int "com.example.tikoppij.ui.components  	IntOffset "com.example.tikoppij.ui.components  List "com.example.tikoppij.ui.components  LocalDensity "com.example.tikoppij.ui.components  MATCH_PARENT "com.example.tikoppij.ui.components  
MaterialTheme "com.example.tikoppij.ui.components  MediaPlayerService "com.example.tikoppij.ui.components  Modifier "com.example.tikoppij.ui.components  MutableInteractionSource "com.example.tikoppij.ui.components  NavBarContentColor "com.example.tikoppij.ui.components  
NavController "com.example.tikoppij.ui.components  NavigationDrawerComponent "com.example.tikoppij.ui.components  OptIn "com.example.tikoppij.ui.components  Painter "com.example.tikoppij.ui.components  Player "com.example.tikoppij.ui.components  R "com.example.tikoppij.ui.components  RoundedCornerShape "com.example.tikoppij.ui.components  SettingItemSwitch "com.example.tikoppij.ui.components  Spacer "com.example.tikoppij.ui.components  String "com.example.tikoppij.ui.components  Suppress "com.example.tikoppij.ui.components  Surface "com.example.tikoppij.ui.components  Switch "com.example.tikoppij.ui.components  Text "com.example.tikoppij.ui.components  TextureView "com.example.tikoppij.ui.components  Unit "com.example.tikoppij.ui.components  UnstableApi "com.example.tikoppij.ui.components  VideoControlButton "com.example.tikoppij.ui.components  VideoControlButtons "com.example.tikoppij.ui.components  VideoDisplayMode "com.example.tikoppij.ui.components  
VideoModel "com.example.tikoppij.ui.components  VideoPlayerComponent "com.example.tikoppij.ui.components  WindowCompat "com.example.tikoppij.ui.components  align "com.example.tikoppij.ui.components  android "com.example.tikoppij.ui.components  androidx "com.example.tikoppij.ui.components  apply "com.example.tikoppij.ui.components  
background "com.example.tikoppij.ui.components  	clickable "com.example.tikoppij.ui.components  clip "com.example.tikoppij.ui.components  
coerceAtLeast "com.example.tikoppij.ui.components  coerceIn "com.example.tikoppij.ui.components  delay "com.example.tikoppij.ui.components  
fillMaxHeight "com.example.tikoppij.ui.components  fillMaxSize "com.example.tikoppij.ui.components  fillMaxWidth "com.example.tikoppij.ui.components  forEach "com.example.tikoppij.ui.components  
graphicsLayer "com.example.tikoppij.ui.components  height "com.example.tikoppij.ui.components  	javaClass "com.example.tikoppij.ui.components  let "com.example.tikoppij.ui.components  offset "com.example.tikoppij.ui.components  onGloballyPositioned "com.example.tikoppij.ui.components  padding "com.example.tikoppij.ui.components  painterResource "com.example.tikoppij.ui.components  pointerInput "com.example.tikoppij.ui.components  provideDelegate "com.example.tikoppij.ui.components  remember "com.example.tikoppij.ui.components  
roundToInt "com.example.tikoppij.ui.components  run "com.example.tikoppij.ui.components  size "com.example.tikoppij.ui.components  stringResource "com.example.tikoppij.ui.components  weight "com.example.tikoppij.ui.components  width "com.example.tikoppij.ui.components  Listener )com.example.tikoppij.ui.components.Player  
AUTO_ADAPT 3com.example.tikoppij.ui.components.VideoDisplayMode  FIT 3com.example.tikoppij.ui.components.VideoDisplayMode  name 3com.example.tikoppij.ui.components.VideoDisplayMode  valueOf 3com.example.tikoppij.ui.components.VideoDisplayMode  view *com.example.tikoppij.ui.components.android  Window /com.example.tikoppij.ui.components.android.view  AppDestinations "com.example.tikoppij.ui.navigation  AppNavGraph "com.example.tikoppij.ui.navigation  Boolean "com.example.tikoppij.ui.navigation  
BottomNavItem "com.example.tikoppij.ui.navigation  CacheManagementScreen "com.example.tikoppij.ui.navigation  
Composable "com.example.tikoppij.ui.navigation  EnterTransition "com.example.tikoppij.ui.navigation  ExitTransition "com.example.tikoppij.ui.navigation  FavoriteListScreen "com.example.tikoppij.ui.navigation  HiddenScreen "com.example.tikoppij.ui.navigation  HistoryListScreen "com.example.tikoppij.ui.navigation  Int "com.example.tikoppij.ui.navigation  List "com.example.tikoppij.ui.navigation  Modifier "com.example.tikoppij.ui.navigation  NavHostController "com.example.tikoppij.ui.navigation  
PaddingValues "com.example.tikoppij.ui.navigation  
ProfileScreen "com.example.tikoppij.ui.navigation  R "com.example.tikoppij.ui.navigation  
ScreenWrapper "com.example.tikoppij.ui.navigation  String "com.example.tikoppij.ui.navigation  ToolsScreen "com.example.tikoppij.ui.navigation  Unit "com.example.tikoppij.ui.navigation  UnstableApi "com.example.tikoppij.ui.navigation  
VideoModel "com.example.tikoppij.ui.navigation  VideoPlayerScreen "com.example.tikoppij.ui.navigation  VideoScreen "com.example.tikoppij.ui.navigation  androidx "com.example.tikoppij.ui.navigation  bottomNavItems "com.example.tikoppij.ui.navigation  fillMaxSize "com.example.tikoppij.ui.navigation  let "com.example.tikoppij.ui.navigation  listOf "com.example.tikoppij.ui.navigation  provideDelegate "com.example.tikoppij.ui.navigation  AppDestinations 2com.example.tikoppij.ui.navigation.AppDestinations  CacheManagement 2com.example.tikoppij.ui.navigation.AppDestinations  FavoriteList 2com.example.tikoppij.ui.navigation.AppDestinations  FavoritePlayer 2com.example.tikoppij.ui.navigation.AppDestinations  Hidden 2com.example.tikoppij.ui.navigation.AppDestinations  HistoryList 2com.example.tikoppij.ui.navigation.AppDestinations  
HistoryPlayer 2com.example.tikoppij.ui.navigation.AppDestinations  Home 2com.example.tikoppij.ui.navigation.AppDestinations  Profile 2com.example.tikoppij.ui.navigation.AppDestinations  String 2com.example.tikoppij.ui.navigation.AppDestinations  Tools 2com.example.tikoppij.ui.navigation.AppDestinations  route 2com.example.tikoppij.ui.navigation.AppDestinations  route Bcom.example.tikoppij.ui.navigation.AppDestinations.CacheManagement  route ?com.example.tikoppij.ui.navigation.AppDestinations.FavoriteList  route Acom.example.tikoppij.ui.navigation.AppDestinations.FavoritePlayer  route 9com.example.tikoppij.ui.navigation.AppDestinations.Hidden  route >com.example.tikoppij.ui.navigation.AppDestinations.HistoryList  route @com.example.tikoppij.ui.navigation.AppDestinations.HistoryPlayer  route 7com.example.tikoppij.ui.navigation.AppDestinations.Home  route :com.example.tikoppij.ui.navigation.AppDestinations.Profile  route 8com.example.tikoppij.ui.navigation.AppDestinations.Tools  route 0com.example.tikoppij.ui.navigation.BottomNavItem  title 0com.example.tikoppij.ui.navigation.BottomNavItem  Activity com.example.tikoppij.ui.screens  AlertDialog com.example.tikoppij.ui.screens  	Alignment com.example.tikoppij.ui.screens  Arrangement com.example.tikoppij.ui.screens  BannerItemData com.example.tikoppij.ui.screens  Boolean com.example.tikoppij.ui.screens  BottomMenuSheet com.example.tikoppij.ui.screens  Box com.example.tikoppij.ui.screens  Button com.example.tikoppij.ui.screens  CacheManagementScreen com.example.tikoppij.ui.screens  CacheManagementViewModel com.example.tikoppij.ui.screens  Card com.example.tikoppij.ui.screens  CardDefaults com.example.tikoppij.ui.screens  CircleShape com.example.tikoppij.ui.screens  CircularProgressIndicator com.example.tikoppij.ui.screens  Color com.example.tikoppij.ui.screens  Column com.example.tikoppij.ui.screens  
Composable com.example.tikoppij.ui.screens  ContentScale com.example.tikoppij.ui.screens  Date com.example.tikoppij.ui.screens  ExperimentalFoundationApi com.example.tikoppij.ui.screens  ExperimentalMaterial3Api com.example.tikoppij.ui.screens  FavoriteListScreen com.example.tikoppij.ui.screens  
FavoriteModel com.example.tikoppij.ui.screens  FavoriteVideoItem com.example.tikoppij.ui.screens  FavoriteViewModel com.example.tikoppij.ui.screens  
FontWeight com.example.tikoppij.ui.screens  HiddenScreen com.example.tikoppij.ui.screens  HistoryListScreen com.example.tikoppij.ui.screens  HistoryModel com.example.tikoppij.ui.screens  HistoryVideoItem com.example.tikoppij.ui.screens  HistoryViewModel com.example.tikoppij.ui.screens  HorizontalDivider com.example.tikoppij.ui.screens  HorizontalPager com.example.tikoppij.ui.screens  Icon com.example.tikoppij.ui.screens  
IconButton com.example.tikoppij.ui.screens  Image com.example.tikoppij.ui.screens  ImageVector com.example.tikoppij.ui.screens  Int com.example.tikoppij.ui.screens  
LazyColumn com.example.tikoppij.ui.screens  LifecycleManager com.example.tikoppij.ui.screens  LinearProgressIndicator com.example.tikoppij.ui.screens  List com.example.tikoppij.ui.screens  ListItem com.example.tikoppij.ui.screens  ListItemDefaults com.example.tikoppij.ui.screens  Locale com.example.tikoppij.ui.screens  Long com.example.tikoppij.ui.screens  
MaterialTheme com.example.tikoppij.ui.screens  Modifier com.example.tikoppij.ui.screens  MyContentMenuCard com.example.tikoppij.ui.screens  
NavController com.example.tikoppij.ui.screens  OptIn com.example.tikoppij.ui.screens  
PaddingValues com.example.tikoppij.ui.screens  
ProfileScreen com.example.tikoppij.ui.screens  R com.example.tikoppij.ui.screens  RadioButton com.example.tikoppij.ui.screens  RoundedCornerShape com.example.tikoppij.ui.screens  Row com.example.tikoppij.ui.screens  Scaffold com.example.tikoppij.ui.screens  SettingMenuItem com.example.tikoppij.ui.screens  SettingsMenuCard com.example.tikoppij.ui.screens  Spacer com.example.tikoppij.ui.screens  StatItem com.example.tikoppij.ui.screens  String com.example.tikoppij.ui.screens  Text com.example.tikoppij.ui.screens  	TextAlign com.example.tikoppij.ui.screens  
TextButton com.example.tikoppij.ui.screens  TextOverflow com.example.tikoppij.ui.screens  ToolsScreen com.example.tikoppij.ui.screens  	TopAppBar com.example.tikoppij.ui.screens  Unit com.example.tikoppij.ui.screens  UnstableApi com.example.tikoppij.ui.screens  UserInfoCard com.example.tikoppij.ui.screens  
VerticalPager com.example.tikoppij.ui.screens  VideoControlButtons com.example.tikoppij.ui.screens  
VideoModel com.example.tikoppij.ui.screens  VideoPlayerComponent com.example.tikoppij.ui.screens  VideoPlayerScreen com.example.tikoppij.ui.screens  VideoPlayerViewModel com.example.tikoppij.ui.screens  VideoScreen com.example.tikoppij.ui.screens  VideoViewModel com.example.tikoppij.ui.screens  WindowCompat com.example.tikoppij.ui.screens  WindowInsetsCompat com.example.tikoppij.ui.screens  align com.example.tikoppij.ui.screens  animateFloatAsState com.example.tikoppij.ui.screens  any com.example.tikoppij.ui.screens  
background com.example.tikoppij.ui.screens  
cardColors com.example.tikoppij.ui.screens  
cardElevation com.example.tikoppij.ui.screens  	clickable com.example.tikoppij.ui.screens  clip com.example.tikoppij.ui.screens  
coerceAtLeast com.example.tikoppij.ui.screens  coerceIn com.example.tikoppij.ui.screens  collectAsState com.example.tikoppij.ui.screens  colors com.example.tikoppij.ui.screens  com com.example.tikoppij.ui.screens  delay com.example.tikoppij.ui.screens  fillMaxSize com.example.tikoppij.ui.screens  fillMaxWidth com.example.tikoppij.ui.screens  forEach com.example.tikoppij.ui.screens  formatFavoriteTime com.example.tikoppij.ui.screens  getValue com.example.tikoppij.ui.screens  
graphicsLayer com.example.tikoppij.ui.screens  height com.example.tikoppij.ui.screens  
isNotEmpty com.example.tikoppij.ui.screens  lerp com.example.tikoppij.ui.screens  listOf com.example.tikoppij.ui.screens  mutableStateOf com.example.tikoppij.ui.screens  observeAppLifecycleWithPlayback com.example.tikoppij.ui.screens  padding com.example.tikoppij.ui.screens  painterResource com.example.tikoppij.ui.screens  provideDelegate com.example.tikoppij.ui.screens  remember com.example.tikoppij.ui.screens  repeat com.example.tikoppij.ui.screens  scale com.example.tikoppij.ui.screens  setValue com.example.tikoppij.ui.screens  size com.example.tikoppij.ui.screens  spacedBy com.example.tikoppij.ui.screens  weight com.example.tikoppij.ui.screens  width com.example.tikoppij.ui.screens  wrapContentHeight com.example.tikoppij.ui.screens  yield com.example.tikoppij.ui.screens  backgroundColor .com.example.tikoppij.ui.screens.BannerItemData  title .com.example.tikoppij.ui.screens.BannerItemData  example #com.example.tikoppij.ui.screens.com  tikoppij +com.example.tikoppij.ui.screens.com.example  model 4com.example.tikoppij.ui.screens.com.example.tikoppij  
VideoModel :com.example.tikoppij.ui.screens.com.example.tikoppij.model  AppDarkGrey com.example.tikoppij.ui.theme  Boolean com.example.tikoppij.ui.theme  Build com.example.tikoppij.ui.theme  Color com.example.tikoppij.ui.theme  
Composable com.example.tikoppij.ui.theme  DarkColorScheme com.example.tikoppij.ui.theme  DingTalkJinBuTiFont com.example.tikoppij.ui.theme  
FontWeight com.example.tikoppij.ui.theme  LightColorScheme com.example.tikoppij.ui.theme  NavBarContentColor com.example.tikoppij.ui.theme  Pink40 com.example.tikoppij.ui.theme  Pink80 com.example.tikoppij.ui.theme  Purple40 com.example.tikoppij.ui.theme  Purple80 com.example.tikoppij.ui.theme  PurpleGrey40 com.example.tikoppij.ui.theme  PurpleGrey80 com.example.tikoppij.ui.theme  R com.example.tikoppij.ui.theme  
TikoppijTheme com.example.tikoppij.ui.theme  
Typography com.example.tikoppij.ui.theme  Unit com.example.tikoppij.ui.theme  VideoPlayerBackground com.example.tikoppij.ui.theme  Boolean com.example.tikoppij.utils  
Composable com.example.tikoppij.utils  DisposableEffect com.example.tikoppij.utils  Int com.example.tikoppij.utils  	Lifecycle com.example.tikoppij.utils  LifecycleEventObserver com.example.tikoppij.utils  LifecycleManager com.example.tikoppij.utils  LifecycleOwner com.example.tikoppij.utils  LocalLifecycleOwner com.example.tikoppij.utils  Log com.example.tikoppij.utils  Long com.example.tikoppij.utils  MediaPlayerService com.example.tikoppij.utils  Pair com.example.tikoppij.utils  PerformanceMonitor com.example.tikoppij.utils  String com.example.tikoppij.utils  System com.example.tikoppij.utils  UnstableApi com.example.tikoppij.utils  mutableMapOf com.example.tikoppij.utils  mutableStateOf com.example.tikoppij.utils  remember com.example.tikoppij.utils  rememberSaveable com.example.tikoppij.utils  set com.example.tikoppij.utils  DisposableEffect +com.example.tikoppij.utils.LifecycleManager  	Lifecycle +com.example.tikoppij.utils.LifecycleManager  LifecycleEventObserver +com.example.tikoppij.utils.LifecycleManager  LocalLifecycleOwner +com.example.tikoppij.utils.LifecycleManager  Pair +com.example.tikoppij.utils.LifecycleManager  mutableStateOf +com.example.tikoppij.utils.LifecycleManager  observeAppLifecycleWithPlayback +com.example.tikoppij.utils.LifecycleManager  remember +com.example.tikoppij.utils.LifecycleManager  rememberSaveable +com.example.tikoppij.utils.LifecycleManager  ENABLE_LOGGING -com.example.tikoppij.utils.PerformanceMonitor  Log -com.example.tikoppij.utils.PerformanceMonitor  System -com.example.tikoppij.utils.PerformanceMonitor  TAG -com.example.tikoppij.utils.PerformanceMonitor  appStartTime -com.example.tikoppij.utils.PerformanceMonitor  
initialize -com.example.tikoppij.utils.PerformanceMonitor  log -com.example.tikoppij.utils.PerformanceMonitor  mutableMapOf -com.example.tikoppij.utils.PerformanceMonitor  recordTimePoint -com.example.tikoppij.utils.PerformanceMonitor  set -com.example.tikoppij.utils.PerformanceMonitor  
timePoints -com.example.tikoppij.utils.PerformanceMonitor  Boolean com.example.tikoppij.video  CACHE_DIRECTORY_NAME com.example.tikoppij.video  Cache com.example.tikoppij.video  CacheDataSource com.example.tikoppij.video  CacheManager com.example.tikoppij.video  ConcurrentHashMap com.example.tikoppij.video  Context com.example.tikoppij.video  DEFAULT_MAX_CACHE_SIZE com.example.tikoppij.video  
DecimalFormat com.example.tikoppij.video  DefaultAllocator com.example.tikoppij.video  DefaultDataSource com.example.tikoppij.video  DefaultLoadControl com.example.tikoppij.video  DefaultMediaSourceFactory com.example.tikoppij.video  DefaultRenderersFactory com.example.tikoppij.video  Dispatchers com.example.tikoppij.video  	Exception com.example.tikoppij.video  	ExoPlayer com.example.tikoppij.video  File com.example.tikoppij.video  IllegalArgumentException com.example.tikoppij.video  Int com.example.tikoppij.video  LeastRecentlyUsedCacheEvictor com.example.tikoppij.video  
LinkedHashMap com.example.tikoppij.video  List com.example.tikoppij.video  Long com.example.tikoppij.video  MAX_CACHE_SIZE com.example.tikoppij.video  MIN_CACHE_SIZE com.example.tikoppij.video  MIN_PLAYABLE_CACHE_SIZE com.example.tikoppij.video  	MediaItem com.example.tikoppij.video  MediaPlayerService com.example.tikoppij.video  
MyApplication com.example.tikoppij.video  NetworkProvider com.example.tikoppij.video  OkHttpDataSource com.example.tikoppij.video  Pair com.example.tikoppij.video  PerformanceMonitor com.example.tikoppij.video  Player com.example.tikoppij.video  PlayerPoolManager com.example.tikoppij.video  PlayerState com.example.tikoppij.video  SimpleCache com.example.tikoppij.video  StandaloneDatabaseProvider com.example.tikoppij.video  String com.example.tikoppij.video  Synchronized com.example.tikoppij.video  System com.example.tikoppij.video  Unit com.example.tikoppij.video  UnstableApi com.example.tikoppij.video  VideoIndexMapper com.example.tikoppij.video  
VideoModel com.example.tikoppij.video  cache com.example.tikoppij.video  cacheDataSourceFactory com.example.tikoppij.video  
coerceAtLeast com.example.tikoppij.video  coerceAtMost com.example.tikoppij.video  coerceIn com.example.tikoppij.video  context com.example.tikoppij.video  	deleteDir com.example.tikoppij.video  distinct com.example.tikoppij.video  evictor com.example.tikoppij.video  find com.example.tikoppij.video  first com.example.tikoppij.video  firstOrNull com.example.tikoppij.video  forEach com.example.tikoppij.video  	getOrNull com.example.tikoppij.video  indices com.example.tikoppij.video  isEmpty com.example.tikoppij.video  let com.example.tikoppij.video  listOf com.example.tikoppij.video  
mapIndexed com.example.tikoppij.video  maxCacheSizeBytes com.example.tikoppij.video  onVideoEnded com.example.tikoppij.video  playerStateMap com.example.tikoppij.video  
plusAssign com.example.tikoppij.video  recordTimePoint com.example.tikoppij.video  	removeAll com.example.tikoppij.video  runBlocking com.example.tikoppij.video  set com.example.tikoppij.video  synchronized com.example.tikoppij.video  toUri com.example.tikoppij.video  withContext com.example.tikoppij.video  Factory *com.example.tikoppij.video.CacheDataSource  Boolean 'com.example.tikoppij.video.CacheManager  CACHE_DIRECTORY_NAME 'com.example.tikoppij.video.CacheManager  Cache 'com.example.tikoppij.video.CacheManager  CacheDataSource 'com.example.tikoppij.video.CacheManager  	Companion 'com.example.tikoppij.video.CacheManager  Context 'com.example.tikoppij.video.CacheManager  DEFAULT_MAX_CACHE_SIZE 'com.example.tikoppij.video.CacheManager  
DecimalFormat 'com.example.tikoppij.video.CacheManager  DefaultDataSource 'com.example.tikoppij.video.CacheManager  Dispatchers 'com.example.tikoppij.video.CacheManager  	Exception 'com.example.tikoppij.video.CacheManager  File 'com.example.tikoppij.video.CacheManager  LeastRecentlyUsedCacheEvictor 'com.example.tikoppij.video.CacheManager  Long 'com.example.tikoppij.video.CacheManager  MAX_CACHE_SIZE 'com.example.tikoppij.video.CacheManager  MIN_CACHE_SIZE 'com.example.tikoppij.video.CacheManager  MIN_PLAYABLE_CACHE_SIZE 'com.example.tikoppij.video.CacheManager  
MyApplication 'com.example.tikoppij.video.CacheManager  NetworkProvider 'com.example.tikoppij.video.CacheManager  OkHttpDataSource 'com.example.tikoppij.video.CacheManager  SimpleCache 'com.example.tikoppij.video.CacheManager  StandaloneDatabaseProvider 'com.example.tikoppij.video.CacheManager  String 'com.example.tikoppij.video.CacheManager  Synchronized 'com.example.tikoppij.video.CacheManager  cache 'com.example.tikoppij.video.CacheManager  cacheDataSourceFactory 'com.example.tikoppij.video.CacheManager  calculateDirSize 'com.example.tikoppij.video.CacheManager  checkCacheStatus 'com.example.tikoppij.video.CacheManager  
clearAllCache 'com.example.tikoppij.video.CacheManager  coerceIn 'com.example.tikoppij.video.CacheManager  context 'com.example.tikoppij.video.CacheManager  	deleteDir 'com.example.tikoppij.video.CacheManager  evictor 'com.example.tikoppij.video.CacheManager  first 'com.example.tikoppij.video.CacheManager  forEach 'com.example.tikoppij.video.CacheManager  formatFileSize 'com.example.tikoppij.video.CacheManager  getCache 'com.example.tikoppij.video.CacheManager  getCacheDataSourceFactory 'com.example.tikoppij.video.CacheManager  getCurrentCacheSize 'com.example.tikoppij.video.CacheManager  getCurrentCacheSizeFormatted 'com.example.tikoppij.video.CacheManager  getMaxCacheSize 'com.example.tikoppij.video.CacheManager  getMaxCacheSizeFormatted 'com.example.tikoppij.video.CacheManager  
initCacheSize 'com.example.tikoppij.video.CacheManager  isCachedForPlayback 'com.example.tikoppij.video.CacheManager  isEmpty 'com.example.tikoppij.video.CacheManager  maxCacheSizeBytes 'com.example.tikoppij.video.CacheManager  minPlayableCacheSizeBytes 'com.example.tikoppij.video.CacheManager  
plusAssign 'com.example.tikoppij.video.CacheManager  runBlocking 'com.example.tikoppij.video.CacheManager  setMaxCacheSize 'com.example.tikoppij.video.CacheManager  toUri 'com.example.tikoppij.video.CacheManager  withContext 'com.example.tikoppij.video.CacheManager  Factory 7com.example.tikoppij.video.CacheManager.CacheDataSource  CACHE_DIRECTORY_NAME 1com.example.tikoppij.video.CacheManager.Companion  CacheDataSource 1com.example.tikoppij.video.CacheManager.Companion  DEFAULT_MAX_CACHE_SIZE 1com.example.tikoppij.video.CacheManager.Companion  
DecimalFormat 1com.example.tikoppij.video.CacheManager.Companion  DefaultDataSource 1com.example.tikoppij.video.CacheManager.Companion  Dispatchers 1com.example.tikoppij.video.CacheManager.Companion  File 1com.example.tikoppij.video.CacheManager.Companion  LeastRecentlyUsedCacheEvictor 1com.example.tikoppij.video.CacheManager.Companion  MAX_CACHE_SIZE 1com.example.tikoppij.video.CacheManager.Companion  MIN_CACHE_SIZE 1com.example.tikoppij.video.CacheManager.Companion  MIN_PLAYABLE_CACHE_SIZE 1com.example.tikoppij.video.CacheManager.Companion  
MyApplication 1com.example.tikoppij.video.CacheManager.Companion  NetworkProvider 1com.example.tikoppij.video.CacheManager.Companion  OkHttpDataSource 1com.example.tikoppij.video.CacheManager.Companion  SimpleCache 1com.example.tikoppij.video.CacheManager.Companion  StandaloneDatabaseProvider 1com.example.tikoppij.video.CacheManager.Companion  cache 1com.example.tikoppij.video.CacheManager.Companion  cacheDataSourceFactory 1com.example.tikoppij.video.CacheManager.Companion  coerceIn 1com.example.tikoppij.video.CacheManager.Companion  context 1com.example.tikoppij.video.CacheManager.Companion  	deleteDir 1com.example.tikoppij.video.CacheManager.Companion  evictor 1com.example.tikoppij.video.CacheManager.Companion  first 1com.example.tikoppij.video.CacheManager.Companion  forEach 1com.example.tikoppij.video.CacheManager.Companion  isEmpty 1com.example.tikoppij.video.CacheManager.Companion  maxCacheSizeBytes 1com.example.tikoppij.video.CacheManager.Companion  
plusAssign 1com.example.tikoppij.video.CacheManager.Companion  runBlocking 1com.example.tikoppij.video.CacheManager.Companion  toUri 1com.example.tikoppij.video.CacheManager.Companion  withContext 1com.example.tikoppij.video.CacheManager.Companion  CacheManager -com.example.tikoppij.video.MediaPlayerService  PerformanceMonitor -com.example.tikoppij.video.MediaPlayerService  PlayerPoolManager -com.example.tikoppij.video.MediaPlayerService  also -com.example.tikoppij.video.MediaPlayerService  cacheManager -com.example.tikoppij.video.MediaPlayerService  
clearCache -com.example.tikoppij.video.MediaPlayerService  getCurrentCacheSize -com.example.tikoppij.video.MediaPlayerService  getCurrentCacheSizeFormatted -com.example.tikoppij.video.MediaPlayerService  getMaxCacheSize -com.example.tikoppij.video.MediaPlayerService  getMaxCacheSizeFormatted -com.example.tikoppij.video.MediaPlayerService  getOrCreatePlayer -com.example.tikoppij.video.MediaPlayerService  getPlaybackState -com.example.tikoppij.video.MediaPlayerService  getPlayerByIndex -com.example.tikoppij.video.MediaPlayerService  initializePlayers -com.example.tikoppij.video.MediaPlayerService  onVideoEnded -com.example.tikoppij.video.MediaPlayerService  pause -com.example.tikoppij.video.MediaPlayerService  play -com.example.tikoppij.video.MediaPlayerService  playerPoolManager -com.example.tikoppij.video.MediaPlayerService  recordTimePoint -com.example.tikoppij.video.MediaPlayerService  savePlaybackState -com.example.tikoppij.video.MediaPlayerService  setMaxCacheSize -com.example.tikoppij.video.MediaPlayerService  stopPlayerForPage -com.example.tikoppij.video.MediaPlayerService  updateActiveState -com.example.tikoppij.video.MediaPlayerService  Listener !com.example.tikoppij.video.Player  Boolean ,com.example.tikoppij.video.PlayerPoolManager  CacheManager ,com.example.tikoppij.video.PlayerPoolManager  ConcurrentHashMap ,com.example.tikoppij.video.PlayerPoolManager  Context ,com.example.tikoppij.video.PlayerPoolManager  DefaultAllocator ,com.example.tikoppij.video.PlayerPoolManager  DefaultLoadControl ,com.example.tikoppij.video.PlayerPoolManager  DefaultMediaSourceFactory ,com.example.tikoppij.video.PlayerPoolManager  DefaultRenderersFactory ,com.example.tikoppij.video.PlayerPoolManager  	Exception ,com.example.tikoppij.video.PlayerPoolManager  	ExoPlayer ,com.example.tikoppij.video.PlayerPoolManager  IllegalArgumentException ,com.example.tikoppij.video.PlayerPoolManager  Int ,com.example.tikoppij.video.PlayerPoolManager  
LinkedHashMap ,com.example.tikoppij.video.PlayerPoolManager  List ,com.example.tikoppij.video.PlayerPoolManager  Long ,com.example.tikoppij.video.PlayerPoolManager  	MediaItem ,com.example.tikoppij.video.PlayerPoolManager  Player ,com.example.tikoppij.video.PlayerPoolManager  PlayerState ,com.example.tikoppij.video.PlayerPoolManager  String ,com.example.tikoppij.video.PlayerPoolManager  System ,com.example.tikoppij.video.PlayerPoolManager  Unit ,com.example.tikoppij.video.PlayerPoolManager  
VideoModel ,com.example.tikoppij.video.PlayerPoolManager  cacheManager ,com.example.tikoppij.video.PlayerPoolManager  clearPageMapping ,com.example.tikoppij.video.PlayerPoolManager  
coerceAtLeast ,com.example.tikoppij.video.PlayerPoolManager  coerceAtMost ,com.example.tikoppij.video.PlayerPoolManager  coerceIn ,com.example.tikoppij.video.PlayerPoolManager  context ,com.example.tikoppij.video.PlayerPoolManager  createNewPlayer ,com.example.tikoppij.video.PlayerPoolManager  createPlayer ,com.example.tikoppij.video.PlayerPoolManager  distinct ,com.example.tikoppij.video.PlayerPoolManager  find ,com.example.tikoppij.video.PlayerPoolManager  firstOrNull ,com.example.tikoppij.video.PlayerPoolManager  getOrCreatePlayer ,com.example.tikoppij.video.PlayerPoolManager  	getOrNull ,com.example.tikoppij.video.PlayerPoolManager  getPlaybackState ,com.example.tikoppij.video.PlayerPoolManager  getPlayerByIndex ,com.example.tikoppij.video.PlayerPoolManager  indices ,com.example.tikoppij.video.PlayerPoolManager  initializePlayers ,com.example.tikoppij.video.PlayerPoolManager  let ,com.example.tikoppij.video.PlayerPoolManager  listOf ,com.example.tikoppij.video.PlayerPoolManager  lruQueue ,com.example.tikoppij.video.PlayerPoolManager  maxPlayerCount ,com.example.tikoppij.video.PlayerPoolManager  onVideoEnded ,com.example.tikoppij.video.PlayerPoolManager  pageIndexToVideoIdMap ,com.example.tikoppij.video.PlayerPoolManager  pause ,com.example.tikoppij.video.PlayerPoolManager  play ,com.example.tikoppij.video.PlayerPoolManager  playbackStateMap ,com.example.tikoppij.video.PlayerPoolManager  playerStateMap ,com.example.tikoppij.video.PlayerPoolManager  recycleOldPlayersIfNeeded ,com.example.tikoppij.video.PlayerPoolManager  
releaseAll ,com.example.tikoppij.video.PlayerPoolManager  	removeAll ,com.example.tikoppij.video.PlayerPoolManager  savePlaybackState ,com.example.tikoppij.video.PlayerPoolManager  set ,com.example.tikoppij.video.PlayerPoolManager  stopPlayerForPage ,com.example.tikoppij.video.PlayerPoolManager  updateActiveState ,com.example.tikoppij.video.PlayerPoolManager  updateLRUAccess ,com.example.tikoppij.video.PlayerPoolManager  Listener 3com.example.tikoppij.video.PlayerPoolManager.Player  currentPageIndex 8com.example.tikoppij.video.PlayerPoolManager.PlayerState  isActive 8com.example.tikoppij.video.PlayerPoolManager.PlayerState  lastAccessTime 8com.example.tikoppij.video.PlayerPoolManager.PlayerState  let 8com.example.tikoppij.video.PlayerPoolManager.PlayerState  player 8com.example.tikoppij.video.PlayerPoolManager.PlayerState  ConcurrentHashMap +com.example.tikoppij.video.VideoIndexMapper  Pair +com.example.tikoppij.video.VideoIndexMapper  indexToVideoId +com.example.tikoppij.video.VideoIndexMapper  
mapIndexed +com.example.tikoppij.video.VideoIndexMapper  set +com.example.tikoppij.video.VideoIndexMapper  synchronized +com.example.tikoppij.video.VideoIndexMapper  videoIdToIndex +com.example.tikoppij.video.VideoIndexMapper  AndroidViewModel com.example.tikoppij.viewmodel  AppInitializer com.example.tikoppij.viewmodel  Application com.example.tikoppij.viewmodel  Boolean com.example.tikoppij.viewmodel  CacheManagementViewModel com.example.tikoppij.viewmodel  CacheManager com.example.tikoppij.viewmodel  
DecimalFormat com.example.tikoppij.viewmodel  Dispatchers com.example.tikoppij.viewmodel  	Exception com.example.tikoppij.viewmodel  
FavoriteModel com.example.tikoppij.viewmodel  FavoriteRepository com.example.tikoppij.viewmodel  FavoriteViewModel com.example.tikoppij.viewmodel  Float com.example.tikoppij.viewmodel  HistoryModel com.example.tikoppij.viewmodel  HistoryRepository com.example.tikoppij.viewmodel  HistoryViewModel com.example.tikoppij.viewmodel  INITIAL_LOAD_COUNT com.example.tikoppij.viewmodel  Int com.example.tikoppij.viewmodel  LOAD_MORE_COUNT com.example.tikoppij.viewmodel  LOAD_MORE_THRESHOLD com.example.tikoppij.viewmodel  List com.example.tikoppij.viewmodel  Long com.example.tikoppij.viewmodel  MediaPlayerService com.example.tikoppij.viewmodel  MutableStateFlow com.example.tikoppij.viewmodel  
MyApplication com.example.tikoppij.viewmodel  NetworkProvider com.example.tikoppij.viewmodel  PerformanceMonitor com.example.tikoppij.viewmodel  	StateFlow com.example.tikoppij.viewmodel  String com.example.tikoppij.viewmodel  System com.example.tikoppij.viewmodel  Unit com.example.tikoppij.viewmodel  UnstableApi com.example.tikoppij.viewmodel  VideoDisplayMode com.example.tikoppij.viewmodel  
VideoModel com.example.tikoppij.viewmodel  VideoPlayerViewModel com.example.tikoppij.viewmodel  VideoViewModel com.example.tikoppij.viewmodel  _autoPlayNextEnabled com.example.tikoppij.viewmodel  _currentCacheSize com.example.tikoppij.viewmodel  _currentCacheSizeFormatted com.example.tikoppij.viewmodel  
_currentIndex com.example.tikoppij.viewmodel  _currentVideoId com.example.tikoppij.viewmodel  _currentVideoIsFavorite com.example.tikoppij.viewmodel  
_favoriteList com.example.tikoppij.viewmodel  _historyList com.example.tikoppij.viewmodel  _isClearingCache com.example.tikoppij.viewmodel  
_isLoading com.example.tikoppij.viewmodel  
_maxCacheSize com.example.tikoppij.viewmodel  _maxCacheSizeFormatted com.example.tikoppij.viewmodel  _videoDisplayMode com.example.tikoppij.viewmodel  
_videoList com.example.tikoppij.viewmodel  addToHistory com.example.tikoppij.viewmodel  asStateFlow com.example.tikoppij.viewmodel  coerceIn com.example.tikoppij.viewmodel  
collectLatest com.example.tikoppij.viewmodel  currentIndex com.example.tikoppij.viewmodel  drop com.example.tikoppij.viewmodel  	emptyList com.example.tikoppij.viewmodel  favoriteRepository com.example.tikoppij.viewmodel  formatFileSize com.example.tikoppij.viewmodel  getMediaPlayerService com.example.tikoppij.viewmodel  	getOrNull com.example.tikoppij.viewmodel  historyRepository com.example.tikoppij.viewmodel  
isNotEmpty com.example.tikoppij.viewmodel  kotlinx com.example.tikoppij.viewmodel  launch com.example.tikoppij.viewmodel  let com.example.tikoppij.viewmodel  listOf com.example.tikoppij.viewmodel  
loadVideos com.example.tikoppij.viewmodel  map com.example.tikoppij.viewmodel  mediaPlayerService com.example.tikoppij.viewmodel  recordTimePoint com.example.tikoppij.viewmodel  refreshCacheInfo com.example.tikoppij.viewmodel  sortedByDescending com.example.tikoppij.viewmodel  take com.example.tikoppij.viewmodel  
toMutableList com.example.tikoppij.viewmodel  updateCacheUsagePercent com.example.tikoppij.viewmodel   updateCurrentVideoFavoriteStatus com.example.tikoppij.viewmodel  userPreferencesRepository com.example.tikoppij.viewmodel  	videoList com.example.tikoppij.viewmodel  withContext com.example.tikoppij.viewmodel  AppInitializer 7com.example.tikoppij.viewmodel.CacheManagementViewModel  CacheManager 7com.example.tikoppij.viewmodel.CacheManagementViewModel  
DecimalFormat 7com.example.tikoppij.viewmodel.CacheManagementViewModel  MutableStateFlow 7com.example.tikoppij.viewmodel.CacheManagementViewModel  
MyApplication 7com.example.tikoppij.viewmodel.CacheManagementViewModel  _cacheUsagePercent 7com.example.tikoppij.viewmodel.CacheManagementViewModel  _currentCacheSize 7com.example.tikoppij.viewmodel.CacheManagementViewModel  _currentCacheSizeFormatted 7com.example.tikoppij.viewmodel.CacheManagementViewModel  _isClearingCache 7com.example.tikoppij.viewmodel.CacheManagementViewModel  
_maxCacheSize 7com.example.tikoppij.viewmodel.CacheManagementViewModel  _maxCacheSizeFormatted 7com.example.tikoppij.viewmodel.CacheManagementViewModel  asStateFlow 7com.example.tikoppij.viewmodel.CacheManagementViewModel  cacheSizeOptions 7com.example.tikoppij.viewmodel.CacheManagementViewModel  cacheUsagePercent 7com.example.tikoppij.viewmodel.CacheManagementViewModel  
clearAllCache 7com.example.tikoppij.viewmodel.CacheManagementViewModel  coerceIn 7com.example.tikoppij.viewmodel.CacheManagementViewModel  
collectLatest 7com.example.tikoppij.viewmodel.CacheManagementViewModel  currentCacheSizeFormatted 7com.example.tikoppij.viewmodel.CacheManagementViewModel  formatFileSize 7com.example.tikoppij.viewmodel.CacheManagementViewModel  getMediaPlayerService 7com.example.tikoppij.viewmodel.CacheManagementViewModel  invoke 7com.example.tikoppij.viewmodel.CacheManagementViewModel  isClearingCache 7com.example.tikoppij.viewmodel.CacheManagementViewModel  launch 7com.example.tikoppij.viewmodel.CacheManagementViewModel  listOf 7com.example.tikoppij.viewmodel.CacheManagementViewModel  maxCacheSize 7com.example.tikoppij.viewmodel.CacheManagementViewModel  maxCacheSizeFormatted 7com.example.tikoppij.viewmodel.CacheManagementViewModel  mediaPlayerService 7com.example.tikoppij.viewmodel.CacheManagementViewModel  refreshCacheInfo 7com.example.tikoppij.viewmodel.CacheManagementViewModel  setMaxCacheSize 7com.example.tikoppij.viewmodel.CacheManagementViewModel  updateCacheUsagePercent 7com.example.tikoppij.viewmodel.CacheManagementViewModel  userPreferencesRepository 7com.example.tikoppij.viewmodel.CacheManagementViewModel  viewModelScope 7com.example.tikoppij.viewmodel.CacheManagementViewModel  AppInitializer 0com.example.tikoppij.viewmodel.FavoriteViewModel  MutableStateFlow 0com.example.tikoppij.viewmodel.FavoriteViewModel  
MyApplication 0com.example.tikoppij.viewmodel.FavoriteViewModel  
_currentIndex 0com.example.tikoppij.viewmodel.FavoriteViewModel  
_favoriteList 0com.example.tikoppij.viewmodel.FavoriteViewModel  
_isLoading 0com.example.tikoppij.viewmodel.FavoriteViewModel  asStateFlow 0com.example.tikoppij.viewmodel.FavoriteViewModel  clearFavorites 0com.example.tikoppij.viewmodel.FavoriteViewModel  
collectLatest 0com.example.tikoppij.viewmodel.FavoriteViewModel  	emptyList 0com.example.tikoppij.viewmodel.FavoriteViewModel  favoriteList 0com.example.tikoppij.viewmodel.FavoriteViewModel  favoriteRepository 0com.example.tikoppij.viewmodel.FavoriteViewModel  getFavoriteVideosAsVideoModels 0com.example.tikoppij.viewmodel.FavoriteViewModel  getMediaPlayerService 0com.example.tikoppij.viewmodel.FavoriteViewModel  historyRepository 0com.example.tikoppij.viewmodel.FavoriteViewModel  invoke 0com.example.tikoppij.viewmodel.FavoriteViewModel  	isLoading 0com.example.tikoppij.viewmodel.FavoriteViewModel  
isNotEmpty 0com.example.tikoppij.viewmodel.FavoriteViewModel  launch 0com.example.tikoppij.viewmodel.FavoriteViewModel  loadFavoriteList 0com.example.tikoppij.viewmodel.FavoriteViewModel  map 0com.example.tikoppij.viewmodel.FavoriteViewModel  mediaPlayerService 0com.example.tikoppij.viewmodel.FavoriteViewModel  removeFavorite 0com.example.tikoppij.viewmodel.FavoriteViewModel  sortedByDescending 0com.example.tikoppij.viewmodel.FavoriteViewModel  viewModelScope 0com.example.tikoppij.viewmodel.FavoriteViewModel  AppInitializer /com.example.tikoppij.viewmodel.HistoryViewModel  MutableStateFlow /com.example.tikoppij.viewmodel.HistoryViewModel  
MyApplication /com.example.tikoppij.viewmodel.HistoryViewModel  System /com.example.tikoppij.viewmodel.HistoryViewModel  
_currentIndex /com.example.tikoppij.viewmodel.HistoryViewModel  _historyList /com.example.tikoppij.viewmodel.HistoryViewModel  
_isLoading /com.example.tikoppij.viewmodel.HistoryViewModel  asStateFlow /com.example.tikoppij.viewmodel.HistoryViewModel  clearHistory /com.example.tikoppij.viewmodel.HistoryViewModel  
collectLatest /com.example.tikoppij.viewmodel.HistoryViewModel  	emptyList /com.example.tikoppij.viewmodel.HistoryViewModel  formatWatchProgress /com.example.tikoppij.viewmodel.HistoryViewModel  formatWatchTime /com.example.tikoppij.viewmodel.HistoryViewModel  getHistoryVideosAsVideoModels /com.example.tikoppij.viewmodel.HistoryViewModel  getMediaPlayerService /com.example.tikoppij.viewmodel.HistoryViewModel  historyList /com.example.tikoppij.viewmodel.HistoryViewModel  historyRepository /com.example.tikoppij.viewmodel.HistoryViewModel  invoke /com.example.tikoppij.viewmodel.HistoryViewModel  	isLoading /com.example.tikoppij.viewmodel.HistoryViewModel  
isNotEmpty /com.example.tikoppij.viewmodel.HistoryViewModel  launch /com.example.tikoppij.viewmodel.HistoryViewModel  loadHistoryList /com.example.tikoppij.viewmodel.HistoryViewModel  map /com.example.tikoppij.viewmodel.HistoryViewModel  mediaPlayerService /com.example.tikoppij.viewmodel.HistoryViewModel  
removeHistory /com.example.tikoppij.viewmodel.HistoryViewModel  viewModelScope /com.example.tikoppij.viewmodel.HistoryViewModel  AppInitializer 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  MutableStateFlow 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  
MyApplication 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  VideoDisplayMode 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  _autoPlayNextEnabled 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  
_currentIndex 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  _currentVideoIsFavorite 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  _videoDisplayMode 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  
_videoList 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  addToHistory 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  asStateFlow 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  autoPlayNextEnabled 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  
collectLatest 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  currentIndex 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  currentVideoIsFavorite 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  	emptyList 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  favoriteRepository 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  favoriteStatusJob 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  getMediaPlayerService 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  handleVideoEnded 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  historyRepository 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  initializePlaylist 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  invoke 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  
isNotEmpty 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  launch 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  let 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  mediaPlayerService 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  originalOnVideoEnded 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  toggleAutoPlayNextEnabled 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  toggleCurrentVideoFavorite 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  toggleVideoDisplayMode 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  updateCurrentIndex 3com.example.tikoppij.viewmodel.VideoPlayerViewModel   updateCurrentVideoFavoriteStatus 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  userPreferencesRepository 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  videoDisplayMode 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  viewModelScope 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  AppInitializer -com.example.tikoppij.viewmodel.VideoViewModel  Application -com.example.tikoppij.viewmodel.VideoViewModel  Boolean -com.example.tikoppij.viewmodel.VideoViewModel  Dispatchers -com.example.tikoppij.viewmodel.VideoViewModel  	Exception -com.example.tikoppij.viewmodel.VideoViewModel  FavoriteRepository -com.example.tikoppij.viewmodel.VideoViewModel  HistoryRepository -com.example.tikoppij.viewmodel.VideoViewModel  INITIAL_LOAD_COUNT -com.example.tikoppij.viewmodel.VideoViewModel  Int -com.example.tikoppij.viewmodel.VideoViewModel  LOAD_MORE_COUNT -com.example.tikoppij.viewmodel.VideoViewModel  LOAD_MORE_THRESHOLD -com.example.tikoppij.viewmodel.VideoViewModel  List -com.example.tikoppij.viewmodel.VideoViewModel  Long -com.example.tikoppij.viewmodel.VideoViewModel  MediaPlayerService -com.example.tikoppij.viewmodel.VideoViewModel  MutableStateFlow -com.example.tikoppij.viewmodel.VideoViewModel  
MyApplication -com.example.tikoppij.viewmodel.VideoViewModel  NetworkProvider -com.example.tikoppij.viewmodel.VideoViewModel  PerformanceMonitor -com.example.tikoppij.viewmodel.VideoViewModel  	StateFlow -com.example.tikoppij.viewmodel.VideoViewModel  String -com.example.tikoppij.viewmodel.VideoViewModel  Unit -com.example.tikoppij.viewmodel.VideoViewModel  VideoDisplayMode -com.example.tikoppij.viewmodel.VideoViewModel  
VideoModel -com.example.tikoppij.viewmodel.VideoViewModel  _autoPlayNextEnabled -com.example.tikoppij.viewmodel.VideoViewModel  
_currentIndex -com.example.tikoppij.viewmodel.VideoViewModel  _currentVideoId -com.example.tikoppij.viewmodel.VideoViewModel  _currentVideoIsFavorite -com.example.tikoppij.viewmodel.VideoViewModel  
_isLoading -com.example.tikoppij.viewmodel.VideoViewModel  _videoDisplayMode -com.example.tikoppij.viewmodel.VideoViewModel  
_videoList -com.example.tikoppij.viewmodel.VideoViewModel  addToHistory -com.example.tikoppij.viewmodel.VideoViewModel  asStateFlow -com.example.tikoppij.viewmodel.VideoViewModel  autoPlayNextEnabled -com.example.tikoppij.viewmodel.VideoViewModel  
checkLoadMore -com.example.tikoppij.viewmodel.VideoViewModel  
collectLatest -com.example.tikoppij.viewmodel.VideoViewModel  currentIndex -com.example.tikoppij.viewmodel.VideoViewModel  currentVideoIsFavorite -com.example.tikoppij.viewmodel.VideoViewModel  drop -com.example.tikoppij.viewmodel.VideoViewModel  	emptyList -com.example.tikoppij.viewmodel.VideoViewModel  favoriteRepository -com.example.tikoppij.viewmodel.VideoViewModel  favoriteStatusJob -com.example.tikoppij.viewmodel.VideoViewModel  getMediaPlayerService -com.example.tikoppij.viewmodel.VideoViewModel  	getOrNull -com.example.tikoppij.viewmodel.VideoViewModel  handleVideoEnded -com.example.tikoppij.viewmodel.VideoViewModel  historyRepository -com.example.tikoppij.viewmodel.VideoViewModel  invoke -com.example.tikoppij.viewmodel.VideoViewModel  
isNotEmpty -com.example.tikoppij.viewmodel.VideoViewModel  kotlinx -com.example.tikoppij.viewmodel.VideoViewModel  launch -com.example.tikoppij.viewmodel.VideoViewModel  let -com.example.tikoppij.viewmodel.VideoViewModel  loadInitialVideos -com.example.tikoppij.viewmodel.VideoViewModel  
loadVideos -com.example.tikoppij.viewmodel.VideoViewModel  loadVideosCore -com.example.tikoppij.viewmodel.VideoViewModel  mediaPlayerService -com.example.tikoppij.viewmodel.VideoViewModel  recordTimePoint -com.example.tikoppij.viewmodel.VideoViewModel  take -com.example.tikoppij.viewmodel.VideoViewModel  
toMutableList -com.example.tikoppij.viewmodel.VideoViewModel  toggleAutoPlayNextEnabled -com.example.tikoppij.viewmodel.VideoViewModel  toggleCurrentVideoFavorite -com.example.tikoppij.viewmodel.VideoViewModel  toggleVideoDisplayMode -com.example.tikoppij.viewmodel.VideoViewModel  updateCurrentIndex -com.example.tikoppij.viewmodel.VideoViewModel  updateCurrentIndexInternal -com.example.tikoppij.viewmodel.VideoViewModel   updateCurrentVideoFavoriteStatus -com.example.tikoppij.viewmodel.VideoViewModel  updateVideoDisplayMode -com.example.tikoppij.viewmodel.VideoViewModel  userPreferencesRepository -com.example.tikoppij.viewmodel.VideoViewModel  videoDisplayMode -com.example.tikoppij.viewmodel.VideoViewModel  	videoList -com.example.tikoppij.viewmodel.VideoViewModel  viewModelScope -com.example.tikoppij.viewmodel.VideoViewModel  withContext -com.example.tikoppij.viewmodel.VideoViewModel  AppInitializer 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  Dispatchers 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  INITIAL_LOAD_COUNT 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  LOAD_MORE_COUNT 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  LOAD_MORE_THRESHOLD 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  MutableStateFlow 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  
MyApplication 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  NetworkProvider 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  PerformanceMonitor 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  VideoDisplayMode 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  _autoPlayNextEnabled 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  
_currentIndex 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  _currentVideoId 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  _currentVideoIsFavorite 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  
_isLoading 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  _videoDisplayMode 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  
_videoList 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  addToHistory 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  asStateFlow 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  
collectLatest 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  currentIndex 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  drop 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  	emptyList 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  favoriteRepository 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  getMediaPlayerService 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  	getOrNull 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  historyRepository 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  
isNotEmpty 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  launch 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  let 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  
loadVideos 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  mediaPlayerService 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  recordTimePoint 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  take 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  
toMutableList 7com.example.tikoppij.viewmodel.VideoViewModel.Companion   updateCurrentVideoFavoriteStatus 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  userPreferencesRepository 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  	videoList 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  viewModelScope 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  withContext 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  
coroutines 5com.example.tikoppij.viewmodel.VideoViewModel.kotlinx  Job @com.example.tikoppij.viewmodel.VideoViewModel.kotlinx.coroutines  
coroutines &com.example.tikoppij.viewmodel.kotlinx  Job 1com.example.tikoppij.viewmodel.kotlinx.coroutines  Gson com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  	TypeToken com.google.gson.reflect  type !com.google.gson.reflect.TypeToken  File java.io  delete java.io.File  exists java.io.File  isDirectory java.io.File  length java.io.File  	listFiles java.io.File  mkdirs java.io.File  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  StringBuffer 	java.lang  getDeclaredField java.lang.Class  currentTimeMillis java.lang.System  Field java.lang.reflect  Type java.lang.reflect  isAccessible "java.lang.reflect.AccessibleObject  apply java.lang.reflect.Field  get java.lang.reflect.Field  isAccessible java.lang.reflect.Field  
BigDecimal 	java.math  
BigInteger 	java.math  
DecimalFormat 	java.text  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.DecimalFormat  format java.text.Format  format java.text.NumberFormat  format java.text.SimpleDateFormat  AlertDialog 	java.util  	Alignment 	java.util  Arrangement 	java.util  Box 	java.util  Card 	java.util  CardDefaults 	java.util  CircularProgressIndicator 	java.util  Color 	java.util  Column 	java.util  
Composable 	java.util  Date 	java.util  ExperimentalMaterial3Api 	java.util  
FavoriteModel 	java.util  FavoriteVideoItem 	java.util  FavoriteViewModel 	java.util  
FontWeight 	java.util  Icon 	java.util  
IconButton 	java.util  Int 	java.util  
LazyColumn 	java.util  
LinkedHashMap 	java.util  List 	java.util  Locale 	java.util  Long 	java.util  
MaterialTheme 	java.util  Modifier 	java.util  OptIn 	java.util  
PaddingValues 	java.util  R 	java.util  RoundedCornerShape 	java.util  Row 	java.util  Scaffold 	java.util  Spacer 	java.util  String 	java.util  Text 	java.util  
TextButton 	java.util  TextOverflow 	java.util  	TopAppBar 	java.util  Unit 	java.util  UnstableApi 	java.util  align 	java.util  
background 	java.util  
cardElevation 	java.util  clip 	java.util  collectAsState 	java.util  com 	java.util  fillMaxSize 	java.util  fillMaxWidth 	java.util  formatFavoriteTime 	java.util  getValue 	java.util  height 	java.util  
isNotEmpty 	java.util  mutableStateOf 	java.util  padding 	java.util  painterResource 	java.util  provideDelegate 	java.util  remember 	java.util  setValue 	java.util  size 	java.util  spacedBy 	java.util  weight 	java.util  width 	java.util  clear java.util.LinkedHashMap  keys java.util.LinkedHashMap  remove java.util.LinkedHashMap  set java.util.LinkedHashMap  
getDefault java.util.Locale  example 
java.util.com  tikoppij java.util.com.example  model java.util.com.example.tikoppij  
VideoModel $java.util.com.example.tikoppij.model  ConcurrentHashMap java.util.concurrent  TimeUnit java.util.concurrent  clear &java.util.concurrent.ConcurrentHashMap  entries &java.util.concurrent.ConcurrentHashMap  get &java.util.concurrent.ConcurrentHashMap  remove &java.util.concurrent.ConcurrentHashMap  set &java.util.concurrent.ConcurrentHashMap  size &java.util.concurrent.ConcurrentHashMap  values &java.util.concurrent.ConcurrentHashMap  SECONDS java.util.concurrent.TimeUnit  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  Lazy kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  Suppress kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  also kotlin  apply kotlin  getValue kotlin  lazy kotlin  let kotlin  map kotlin  plus kotlin  repeat kotlin  run kotlin  synchronized kotlin  forEach kotlin.Array  isEmpty kotlin.Array  iterator kotlin.Array  not kotlin.Boolean  	compareTo 
kotlin.Double  div 
kotlin.Double  sp 
kotlin.Double  
absoluteValue kotlin.Float  
coerceAtLeast kotlin.Float  coerceIn kotlin.Float  	compareTo kotlin.Float  div kotlin.Float  dp kotlin.Float  invoke kotlin.Float  minus kotlin.Float  plus kotlin.Float  
roundToInt kotlin.Float  times kotlin.Float  toLong kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  
coerceAtLeast 
kotlin.Int  coerceAtMost 
kotlin.Int  coerceIn 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  let 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  coerceIn kotlin.Long  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  plus kotlin.Long  
plusAssign kotlin.Long  rem kotlin.Long  times kotlin.Long  toFloat kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  
isNotEmpty 
kotlin.String  toUri 
kotlin.String  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableCollection kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  any kotlin.collections  distinct kotlin.collections  drop kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  find kotlin.collections  firstOrNull kotlin.collections  forEach kotlin.collections  	getOrNull kotlin.collections  getValue kotlin.collections  indices kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapIndexed kotlin.collections  mutableMapOf kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  	removeAll kotlin.collections  set kotlin.collections  sortedByDescending kotlin.collections  take kotlin.collections  
toMutableList kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  any kotlin.collections.List  distinct kotlin.collections.List  drop kotlin.collections.List  filter kotlin.collections.List  get kotlin.collections.List  	getOrNull kotlin.collections.List  indices kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  let kotlin.collections.List  map kotlin.collections.List  
mapIndexed kotlin.collections.List  plus kotlin.collections.List  size kotlin.collections.List  sortedByDescending kotlin.collections.List  take kotlin.collections.List  
toMutableList kotlin.collections.List  find $kotlin.collections.MutableCollection  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  addAll kotlin.collections.MutableList  MutableEntry kotlin.collections.MutableMap  set kotlin.collections.MutableMap  value *kotlin.collections.MutableMap.MutableEntry  firstOrNull kotlin.collections.MutableSet  iterator kotlin.collections.MutableSet  	removeAll kotlin.collections.MutableSet  SuspendFunction1 kotlin.coroutines  Synchronized 
kotlin.jvm  Volatile 
kotlin.jvm  java 
kotlin.jvm  	javaClass 
kotlin.jvm  
absoluteValue kotlin.math  getAbsoluteValue kotlin.math  
roundToInt kotlin.math  ReadOnlyProperty kotlin.properties  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  IntRange 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  coerceIn 
kotlin.ranges  firstOrNull 
kotlin.ranges  KClass kotlin.reflect  
KFunction1 kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  
KProperty2 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  any kotlin.sequences  distinct kotlin.sequences  drop kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  firstOrNull kotlin.sequences  forEach kotlin.sequences  map kotlin.sequences  
mapIndexed kotlin.sequences  plus kotlin.sequences  sortedByDescending kotlin.sequences  take kotlin.sequences  
toMutableList kotlin.sequences  any kotlin.text  drop kotlin.text  filter kotlin.text  find kotlin.text  firstOrNull kotlin.text  forEach kotlin.text  	getOrNull kotlin.text  indices kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  
mapIndexed kotlin.text  plus kotlin.text  repeat kotlin.text  set kotlin.text  take kotlin.text  
toMutableList kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  	MainScope kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  runBlocking kotlinx.coroutines  withContext kotlinx.coroutines  yield kotlinx.coroutines  AppDestinations !kotlinx.coroutines.CoroutineScope  CACHE_DIRECTORY_NAME !kotlinx.coroutines.CoroutineScope  Dispatchers !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  INITIAL_LOAD_COUNT !kotlinx.coroutines.CoroutineScope  LeastRecentlyUsedCacheEvictor !kotlinx.coroutines.CoroutineScope  MAX_CACHE_SIZE !kotlinx.coroutines.CoroutineScope  MIN_CACHE_SIZE !kotlinx.coroutines.CoroutineScope  
MyApplication !kotlinx.coroutines.CoroutineScope  NetworkProvider !kotlinx.coroutines.CoroutineScope  PerformanceMonitor !kotlinx.coroutines.CoroutineScope  Player !kotlinx.coroutines.CoroutineScope  SimpleCache !kotlinx.coroutines.CoroutineScope  StandaloneDatabaseProvider !kotlinx.coroutines.CoroutineScope  VideoDisplayMode !kotlinx.coroutines.CoroutineScope  WindowCompat !kotlinx.coroutines.CoroutineScope  WindowInsetsCompat !kotlinx.coroutines.CoroutineScope  _autoPlayNextEnabled !kotlinx.coroutines.CoroutineScope  _currentCacheSize !kotlinx.coroutines.CoroutineScope  _currentCacheSizeFormatted !kotlinx.coroutines.CoroutineScope  
_currentIndex !kotlinx.coroutines.CoroutineScope  _currentVideoId !kotlinx.coroutines.CoroutineScope  _currentVideoIsFavorite !kotlinx.coroutines.CoroutineScope  
_favoriteList !kotlinx.coroutines.CoroutineScope  _historyList !kotlinx.coroutines.CoroutineScope  _isClearingCache !kotlinx.coroutines.CoroutineScope  
_isLoading !kotlinx.coroutines.CoroutineScope  
_maxCacheSize !kotlinx.coroutines.CoroutineScope  _maxCacheSizeFormatted !kotlinx.coroutines.CoroutineScope  _videoDisplayMode !kotlinx.coroutines.CoroutineScope  
_videoList !kotlinx.coroutines.CoroutineScope  addToHistory !kotlinx.coroutines.CoroutineScope  backPressedOnce !kotlinx.coroutines.CoroutineScope  cache !kotlinx.coroutines.CoroutineScope  cacheDataSourceFactory !kotlinx.coroutines.CoroutineScope  coerceIn !kotlinx.coroutines.CoroutineScope  
collectLatest !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  currentIndex !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  	deleteDir !kotlinx.coroutines.CoroutineScope  drop !kotlinx.coroutines.CoroutineScope  	emptyList !kotlinx.coroutines.CoroutineScope  evictor !kotlinx.coroutines.CoroutineScope  favoriteRepository !kotlinx.coroutines.CoroutineScope  first !kotlinx.coroutines.CoroutineScope  forEach !kotlinx.coroutines.CoroutineScope  formatFileSize !kotlinx.coroutines.CoroutineScope  historyRepository !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  
loadVideos !kotlinx.coroutines.CoroutineScope  maxCacheSizeBytes !kotlinx.coroutines.CoroutineScope  mediaPlayerService !kotlinx.coroutines.CoroutineScope  recordTimePoint !kotlinx.coroutines.CoroutineScope  refreshCacheInfo !kotlinx.coroutines.CoroutineScope  sortedByDescending !kotlinx.coroutines.CoroutineScope  take !kotlinx.coroutines.CoroutineScope  updateCacheUsagePercent !kotlinx.coroutines.CoroutineScope   updateCurrentVideoFavoriteStatus !kotlinx.coroutines.CoroutineScope  userPreferencesRepository !kotlinx.coroutines.CoroutineScope  	videoList !kotlinx.coroutines.CoroutineScope  window !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  yield !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  cancel kotlinx.coroutines.Job  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  
collectLatest kotlinx.coroutines.flow  first kotlinx.coroutines.flow  map kotlinx.coroutines.flow  
collectLatest kotlinx.coroutines.flow.Flow  first kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  
collectLatest !kotlinx.coroutines.flow.StateFlow  OkHttpClient okhttp3  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  followRedirects okhttp3.OkHttpClient.Builder  followSslRedirects okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  retryOnConnectionFailure okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  HttpLoggingInterceptor okhttp3.logging  Level &okhttp3.logging.HttpLoggingInterceptor  apply &okhttp3.logging.HttpLoggingInterceptor  level &okhttp3.logging.HttpLoggingInterceptor  NONE ,okhttp3.logging.HttpLoggingInterceptor.Level  Response 	retrofit2  Retrofit 	retrofit2  body retrofit2.Response  isSuccessful retrofit2.Response  Builder retrofit2.Retrofit  create retrofit2.Retrofit  addConverterFactory retrofit2.Retrofit.Builder  baseUrl retrofit2.Retrofit.Builder  build retrofit2.Retrofit.Builder  client retrofit2.Retrofit.Builder  GsonConverterFactory retrofit2.converter.gson  create -retrofit2.converter.gson.GsonConverterFactory  GET retrofit2.http  Query retrofit2.http  mediaId  androidx.media3.common.MediaItem  clearMediaItems androidx.media3.common.Player  currentMediaItem androidx.media3.common.Player  stop androidx.media3.common.Player  	MediaItem #androidx.media3.exoplayer.ExoPlayer  apply #androidx.media3.exoplayer.ExoPlayer  cacheManager #androidx.media3.exoplayer.ExoPlayer  clearMediaItems #androidx.media3.exoplayer.ExoPlayer  currentMediaItem #androidx.media3.exoplayer.ExoPlayer  stop #androidx.media3.exoplayer.ExoPlayer  apply com.example.tikoppij.video  arrayOfNulls com.example.tikoppij.video  cacheManager com.example.tikoppij.video  playerSlots com.example.tikoppij.video  VideoIndexMapper -com.example.tikoppij.video.MediaPlayerService  
clearAllCache -com.example.tikoppij.video.MediaPlayerService  
updateMapping -com.example.tikoppij.video.MediaPlayerService  updateMappingBatch -com.example.tikoppij.video.MediaPlayerService  videoIndexMapper -com.example.tikoppij.video.MediaPlayerService  apply ,com.example.tikoppij.video.PlayerPoolManager  arrayOfNulls ,com.example.tikoppij.video.PlayerPoolManager  getSlotIndexForPage ,com.example.tikoppij.video.PlayerPoolManager  playerSlotCount ,com.example.tikoppij.video.PlayerPoolManager  playerSlots ,com.example.tikoppij.video.PlayerPoolManager  	pageIndex 8com.example.tikoppij.video.PlayerPoolManager.PlayerState  videoId 8com.example.tikoppij.video.PlayerPoolManager.PlayerState  
updateMapping +com.example.tikoppij.video.VideoIndexMapper  updateMappingBatch +com.example.tikoppij.video.VideoIndexMapper  map 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  map -com.example.tikoppij.viewmodel.VideoViewModel  map 7com.example.tikoppij.viewmodel.VideoViewModel.Companion  arrayOfNulls kotlin  find kotlin.Array  get kotlin.Array  set kotlin.Array  arrayOfNulls kotlin.collections  map !kotlinx.coroutines.CoroutineScope  forEachIndexed com.example.tikoppij.video  
clearAllSlots -com.example.tikoppij.video.MediaPlayerService  
clearAllSlots ,com.example.tikoppij.video.PlayerPoolManager  forEachIndexed ,com.example.tikoppij.video.PlayerPoolManager  forEachIndexed kotlin.Array  forEachIndexed kotlin.collections  forEachIndexed kotlin.sequences  forEachIndexed kotlin.text  e android.util.Log  w android.util.Log  forEachIndexed +androidx.compose.foundation.layout.BoxScope  android .androidx.compose.foundation.lazy.LazyItemScope  forEachIndexed .androidx.compose.foundation.lazy.LazyItemScope  android .androidx.compose.foundation.lazy.LazyListScope  forEachIndexed .androidx.compose.foundation.lazy.LazyListScope  android com.example.tikoppij.ui.screens  forEachIndexed com.example.tikoppij.ui.screens  android com.example.tikoppij.video  android ,com.example.tikoppij.video.PlayerPoolManager  android com.example.tikoppij.viewmodel  android 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  forEachIndexed kotlin.collections.List  android !kotlinx.coroutines.CoroutineScope  android ,androidx.compose.foundation.pager.PagerScope  scrollToPage ,androidx.compose.foundation.pager.PagerState  forEach ,com.example.tikoppij.video.PlayerPoolManager  updatePlayerContent ,com.example.tikoppij.video.PlayerPoolManager  initializePlayersOnly 3com.example.tikoppij.viewmodel.VideoPlayerViewModel  Long kotlin  	Companion kotlin.Long  	MAX_VALUE kotlin.Long  	MAX_VALUE kotlin.Long.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    