package com.example.tikoppij.ui.screens

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.VerticalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.ui.components.BottomMenuSheet
import com.example.tikoppij.ui.components.VideoControlButtons
import com.example.tikoppij.ui.components.VideoPlayerComponent
import com.example.tikoppij.utils.LifecycleManager
import com.example.tikoppij.viewmodel.VideoPlayerViewModel

/**
 * 独立的视频播放页面
 * 用于收藏列表和历史记录的视频播放
 */
@UnstableApi
@Composable
fun VideoPlayerScreen(
    videoList: List<VideoModel>,
    startIndex: Int = 0,
    modifier: Modifier = Modifier,
    viewModel: VideoPlayerViewModel = viewModel(),
    isBottomBarVisible: Boolean = false
) {
    val currentIndex by viewModel.currentIndex.collectAsState()
    val displayMode by viewModel.videoDisplayMode.collectAsState()
    val autoPlayNextEnabled by viewModel.autoPlayNextEnabled.collectAsState()
    val currentVideoIsFavorite by viewModel.currentVideoIsFavorite.collectAsState()

    var currentVideoIsPlaying by remember { mutableStateOf(true) }
    var playbackStateRefreshTrigger by remember { mutableIntStateOf(0) }
    
    // 菜单相关状态 - 与VideoScreen保持一致
    var isMenuVisible by remember { mutableStateOf(false) }
    
    // 状态栏显示状态 - 收藏播放页面独立管理状态栏
    var isStatusBarVisibleLocal by remember { mutableStateOf(true) }
    
    val context = LocalContext.current
    val activity = context as? Activity

    // 监控菜单显示状态，并在菜单关闭时手动控制状态栏 - 与VideoScreen保持一致
    LaunchedEffect(isMenuVisible, isStatusBarVisibleLocal) {
        if (!isMenuVisible && activity != null) {
            val window = activity.window
            val controller = WindowCompat.getInsetsController(window, window.decorView)
            // 设置状态栏文字为白色（与首页保持一致）
            controller.isAppearanceLightStatusBars = false
            if (isStatusBarVisibleLocal) {
                controller.show(WindowInsetsCompat.Type.statusBars())
            } else {
                controller.hide(WindowInsetsCompat.Type.statusBars())
            }
        }
    }

    // 状态栏切换函数
    fun toggleStatusBarVisibility() {
        isStatusBarVisibleLocal = !isStatusBarVisibleLocal
        if (activity != null) {
            val window = activity.window
            val controller = WindowCompat.getInsetsController(window, window.decorView)
            // 设置状态栏文字为白色（与首页保持一致）
            controller.isAppearanceLightStatusBars = false
            if (isStatusBarVisibleLocal) {
                controller.show(WindowInsetsCompat.Type.statusBars())
            } else {
                controller.hide(WindowInsetsCompat.Type.statusBars())
            }
        }
    }

    // 初始化播放列表
    LaunchedEffect(videoList, startIndex) {
        viewModel.initializePlaylist(videoList, startIndex)
    }

    val isAppInForeground = LifecycleManager.observeAppLifecycleWithPlayback(
        playerManager = viewModel.mediaPlayerService,
        currentIndex = currentIndex
    )
    
    val pagerState = rememberPagerState(initialPage = startIndex) { videoList.size.coerceAtLeast(1) }

    // 同步pager和viewmodel的索引
    LaunchedEffect(pagerState.currentPage, videoList) {
        if (videoList.isNotEmpty() && pagerState.currentPage < videoList.size) {
            val newIndex = pagerState.currentPage
            if (newIndex != currentIndex) {
                viewModel.updateCurrentIndex(newIndex)
            }
            currentVideoIsPlaying = viewModel.mediaPlayerService.getPlaybackState(newIndex) ?: true
            playbackStateRefreshTrigger++
        }
    }

    LaunchedEffect(currentIndex, videoList.size) { 
        if (videoList.isNotEmpty() && currentIndex < videoList.size && pagerState.currentPage != currentIndex) {
            pagerState.animateScrollToPage(currentIndex)
            currentVideoIsPlaying = viewModel.mediaPlayerService.getPlaybackState(currentIndex) ?: true
            playbackStateRefreshTrigger++
        }
    }

    // 同步播放状态到MediaPlayerService
    LaunchedEffect(currentVideoIsPlaying, currentIndex) {
        viewModel.mediaPlayerService.savePlaybackState(currentIndex, currentVideoIsPlaying)
    }

    Box(modifier = modifier.fillMaxSize()) {
        VerticalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            val isCurrentPage = page == pagerState.currentPage
            val isVisible = pagerState.layoutInfo.visiblePagesInfo.any { it.index == page } && isAppInForeground
            
            Box(modifier = Modifier.fillMaxSize()) {
                if (videoList.isNotEmpty() && page < videoList.size) {
                    VideoPlayerComponent(
                        video = videoList[page],
                        playerManager = viewModel.mediaPlayerService,
                        pageIndex = page,
                        isCurrentItem = isCurrentPage,
                        isVisible = isVisible,
                        displayMode = displayMode,
                        onIsPlayingChanged = {
                            if (isCurrentPage) {
                                currentVideoIsPlaying = it
                            }
                        },
                        onLongPress = { // 添加长按菜单功能 - 与VideoScreen保持一致
                            isMenuVisible = true
                        }
                    )
                } else {
                    Box(Modifier.fillMaxSize().background(Color.Black))
                }
                
                // 控制按钮的可见性逻辑 - 与VideoScreen保持完全一致
                val shouldShowButtons = if (isBottomBarVisible) {
                    true // 如果导航栏可见，按钮始终可见
                } else {
                    true // 收藏播放页面：按钮始终可见（与首页保持一致）
                }
                
                // 只在当前页面且满足显示条件时才显示按钮
                if (isCurrentPage && shouldShowButtons && isVisible) {
                    val bottomNavBarHeight = 48.dp
                    val controlsBottomPadding = if (isBottomBarVisible) {
                        16.dp + bottomNavBarHeight
                    } else {
                        // 导航栏隐藏时
                        if (!currentVideoIsPlaying) { // 视频暂停时（显示按钮）
                            // 提高按钮位置以避免与进度条重叠
                            48.dp 
                        } else { // 视频播放时（隐藏按钮）
                            16.dp
                        }
                    }

                    VideoControlButtons(
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .padding(end = 16.dp, bottom = controlsBottomPadding),
                        isFavorite = currentVideoIsFavorite,
                        onFavoriteClick = { viewModel.toggleCurrentVideoFavorite() },
                        onDownloadClick = { /* 下载功能 */ },
                        onShareClick = { /* 分享功能 */ },
                        onMenuClick = { isMenuVisible = true },
                        onToggleBottomBarVisibility = { toggleStatusBarVisibility() },
                        isBottomBarVisible = isStatusBarVisibleLocal,
                        showToggleButton = true,
                        showMenuButton = true
                    )
                }
            }
        }
        
        // 底部菜单 - 与VideoScreen保持完全一致
        BottomMenuSheet(
            isVisible = isMenuVisible,
            onDismiss = { isMenuVisible = false },
            currentDisplayMode = displayMode,
            onToggleDisplayMode = { viewModel.toggleVideoDisplayMode() },
            isAutoPlayNextEnabled = autoPlayNextEnabled,
            onToggleAutoPlayNext = { viewModel.toggleAutoPlayNextEnabled() },
            isStatusBarVisible = isStatusBarVisibleLocal
        )
    }
} 