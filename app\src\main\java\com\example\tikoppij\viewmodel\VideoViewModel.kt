package com.example.tikoppij.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.MyApplication
import com.example.tikoppij.data.FavoriteRepository
import com.example.tikoppij.data.HistoryRepository
import com.example.tikoppij.initializer.AppInitializer
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.network.NetworkProvider
import com.example.tikoppij.ui.components.VideoDisplayMode
import com.example.tikoppij.utils.PerformanceMonitor
import com.example.tikoppij.video.MediaPlayerService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 视频ViewModel
 * 管理视频数据和播放状态
 */
@UnstableApi
class VideoViewModel(application: Application) : AndroidViewModel(application) {
    // 常量定义
    companion object {
        private const val INITIAL_LOAD_COUNT = 3
        private const val LOAD_MORE_COUNT = 10 // API 限制，实际获取可能不同
        private const val LOAD_MORE_THRESHOLD = 5
    }

    // 视频列表数据
    private val _videoList = MutableStateFlow<List<VideoModel>>(emptyList())
    val videoList: StateFlow<List<VideoModel>> = _videoList.asStateFlow()

    // 当前播放的视频索引
    private val _currentIndex = MutableStateFlow(0)
    val currentIndex: StateFlow<Int> = _currentIndex.asStateFlow()

    // 当前播放的视频ID
    private val _currentVideoId = MutableStateFlow<String?>(null)

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    
    // 用户偏好设置仓库
    private val userPreferencesRepository = MyApplication.userPreferencesRepository
    
    // 收藏管理仓库
    private val favoriteRepository: FavoriteRepository = MyApplication.favoriteRepository
    
    // 历史记录管理仓库
    private val historyRepository: HistoryRepository = MyApplication.historyRepository

    // 视频显示模式
    private val _videoDisplayMode = MutableStateFlow(VideoDisplayMode.AUTO_ADAPT)
    val videoDisplayMode: StateFlow<VideoDisplayMode> = _videoDisplayMode.asStateFlow()

    // 连播状态
    private val _autoPlayNextEnabled = MutableStateFlow(false)
    val autoPlayNextEnabled: StateFlow<Boolean> = _autoPlayNextEnabled.asStateFlow()
    
    // 当前视频的收藏状态
    private val _currentVideoIsFavorite = MutableStateFlow(false)
    val currentVideoIsFavorite: StateFlow<Boolean> = _currentVideoIsFavorite.asStateFlow()

    // 播放器服务
    val mediaPlayerService: MediaPlayerService = AppInitializer.getMediaPlayerService(application)

    // 更新当前视频的收藏状态的Job
    private var favoriteStatusJob: kotlinx.coroutines.Job? = null

    // 初始化时加载视频
    init {
        // 记录ViewModel初始化时间点
        PerformanceMonitor.recordTimePoint("ViewModel初始化")
        
        // 加载初始视频数据
        loadInitialVideos()
        
        // 从DataStore加载显示模式设置
        viewModelScope.launch {
            userPreferencesRepository.videoDisplayMode.collectLatest { mode ->
                _videoDisplayMode.value = mode
            }
        }

        // 从DataStore加载连播设置
        viewModelScope.launch {
            userPreferencesRepository.autoPlayNextEnabled.collectLatest { enabled ->
                _autoPlayNextEnabled.value = enabled
            }
        }

        // 设置视频结束回调
        mediaPlayerService.onVideoEnded = {
            handleVideoEnded()
        }
        
        // 监听当前视频索引变化，更新收藏状态和历史记录
        viewModelScope.launch {
            currentIndex.collectLatest { index ->
                if (_videoList.value.isNotEmpty() && index < _videoList.value.size) {
                    val currentVideo = _videoList.value[index]
                    _currentVideoId.value = currentVideo.video_id
                    
                    // 更新收藏状态
                    updateCurrentVideoFavoriteStatus(currentVideo.video_id)
                    
                    // 立即添加到历史记录（播放开始时）
                    addToHistory(currentVideo)
                }
            }
        }
        
        // 监听视频列表变化，确保第一个视频也被记录到历史并更新收藏状态
        viewModelScope.launch {
            videoList.collectLatest { videos ->
                if (videos.isNotEmpty() && _currentIndex.value < videos.size) {
                    val currentVideo = videos[_currentIndex.value]
                    // 确保当前视频在历史记录中
                    addToHistory(currentVideo)
                    // 确保收藏状态正确初始化
                    updateCurrentVideoFavoriteStatus(currentVideo.video_id)
                }
            }
        }
    }

    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        // 取消收藏状态监听
        favoriteStatusJob?.cancel()
        // 停止当前页面的播放
        mediaPlayerService.stopPlayerForPage(_currentIndex.value)
        // 注意：MediaPlayerService由AppInitializer管理，不在这里释放
    }
    
    /**
     * 更新视频显示模式
     * @param mode 新的显示模式
     */
    private fun updateVideoDisplayMode(mode: VideoDisplayMode) {
        if (_videoDisplayMode.value != mode) {
            // 保存到DataStore (在Repository中处理UI线程安全)
            viewModelScope.launch {
                userPreferencesRepository.updateVideoDisplayMode(mode)
            }
        }
    }

    /**
     * 切换视频显示模式
     */
    fun toggleVideoDisplayMode() {
        val newMode = when (_videoDisplayMode.value) {
            VideoDisplayMode.AUTO_ADAPT -> VideoDisplayMode.FIT
            VideoDisplayMode.FIT -> VideoDisplayMode.AUTO_ADAPT
        }
        updateVideoDisplayMode(newMode)
    }

    /**
     * 切换连播状态
     */
    fun toggleAutoPlayNextEnabled() {
        val newValue = !_autoPlayNextEnabled.value
        viewModelScope.launch {
            userPreferencesRepository.updateAutoPlayNextEnabled(newValue)
            // _autoPlayNextEnabled.value 会通过上面的 collectLatest 更新
        }
    }
    
    /**
     * 切换当前视频的收藏状态
     */
    fun toggleCurrentVideoFavorite() {
        viewModelScope.launch {
            if (_videoList.value.isNotEmpty() && _currentIndex.value < _videoList.value.size) {
                val currentVideo = _videoList.value[_currentIndex.value]
                
                if (_currentVideoIsFavorite.value) {
                    // 取消收藏
                    favoriteRepository.removeFavorite(currentVideo.video_id)
                } else {
                    // 添加收藏
                    favoriteRepository.addFavorite(currentVideo)
                }
            }
        }
    }
    
    /**
     * 更新当前视频的收藏状态
     */
    private fun updateCurrentVideoFavoriteStatus(videoId: String) {
        // 取消之前的监听
        favoriteStatusJob?.cancel()
        
        favoriteStatusJob = viewModelScope.launch {
            favoriteRepository.isFavorite(videoId).collectLatest { isFavorite ->
                _currentVideoIsFavorite.value = isFavorite
            }
        }
    }
    
    /**
     * 添加视频到历史记录
     */
    private fun addToHistory(video: VideoModel) {
        viewModelScope.launch {
            historyRepository.addHistory(video)
        }
    }
    
    /**
     * 更新观看进度
     */
    fun updateWatchProgress(videoId: String, progress: Long) {
        viewModelScope.launch {
            historyRepository.updateWatchProgress(videoId, progress)
        }
    }
    
    /**
     * 加载视频数据（抽象方法）
     * 封装视频加载的核心逻辑，可重用于不同场景
     * @param skip 跳过的数量
     * @param count 请求的数量
     * @param onSuccess 成功回调
     */
    private fun loadVideosCore(
        skip: Int = 0,
        count: Int = LOAD_MORE_COUNT,
        onSuccess: (videos: List<VideoModel>, startIndex: Int) -> Unit
    ) {
        if (_isLoading.value) return
        
        viewModelScope.launch(Dispatchers.IO) {
            withContext(Dispatchers.Main) {
                _isLoading.value = true
            }
            
            try {
                // 请求视频数据
                val response = NetworkProvider.apiService.getVideoList(count = count)
                
                if (response.isSuccessful && response.body() != null) {
                    // 获取新视频并跳过已加载部分
                    val newVideos = response.body()!!.data.list
                        .let { if (skip > 0) it.drop(skip) else it }
                    
                    // 切换到主线程更新UI
                    withContext(Dispatchers.Main) {
                        if (newVideos.isNotEmpty()) {
                            val startIndex = _videoList.value.size
                            onSuccess(newVideos, startIndex)
                        }
                    }
                }
            } catch (_: Exception) {
                // 错误处理采用静默方式
            } finally {
                withContext(Dispatchers.Main) {
                    _isLoading.value = false
                }
            }
        }
    }
    
    /**
     * 加载初始视频数据
     * 分离出初始加载逻辑，提高可维护性
     */
    private fun loadInitialVideos() {
        // 使用IO调度器异步加载首屏数据，避免阻塞UI线程
        viewModelScope.launch(Dispatchers.IO) {
            PerformanceMonitor.recordTimePoint("开始加载首屏数据")

            // 加载小批量数据
            val initialVideos = try {
                val response = NetworkProvider.apiService.getVideoList(count = INITIAL_LOAD_COUNT)
                if (response.isSuccessful && response.body() != null) {
                    response.body()!!.data.list.take(INITIAL_LOAD_COUNT)
                } else {
                    emptyList()
                }
            } catch (_: Exception) {
                emptyList()
            }

            // 切换到主线程更新UI
            withContext(Dispatchers.Main) {
                if (initialVideos.isNotEmpty()) {
                    // 更新UI状态
                    _videoList.value = initialVideos
                    
                    // 更新映射关系
                    mediaPlayerService.updateMappingBatch(0, initialVideos.map { it.video_id })
                    
                    // 初始化播放器
                    mediaPlayerService.initializePlayers(initialVideos, _currentIndex.value)
                }
            }

            // 后台继续加载更多数据，不阻塞UI
            loadVideos(initialVideos.size)
        }
    }

    /**
     * 加载视频数据
     * 统一处理视频加载逻辑，支持初始加载和加载更多
     * @param skip 跳过的视频数量，默认为0表示不跳过
     * @param count 请求的视频数量
     */
    private fun loadVideos(skip: Int = 0, count: Int = LOAD_MORE_COUNT) {
        loadVideosCore(skip, count) { newVideos, startIndex ->
            // 更新列表
            val currentList = _videoList.value.toMutableList()
            currentList.addAll(newVideos)
            _videoList.value = currentList
            
            // 更新映射关系
            mediaPlayerService.updateMappingBatch(startIndex, newVideos.map { it.video_id })
            
            // 如果是首次加载，初始化播放器
            if (startIndex == 0) {
                mediaPlayerService.initializePlayers(currentList, _currentIndex.value)
            }
        }
    }

    /**
     * 检查是否需要加载更多视频
     */
    private fun checkLoadMore(currentIndex: Int) {
        val videos = _videoList.value

        // 如果已经接近列表末尾，加载更多
        if (currentIndex >= videos.size - LOAD_MORE_THRESHOLD && !_isLoading.value) {
            loadVideos()
        }
    }

    /**
     * 更新当前播放索引
     * @param index 新的索引
     */
    fun updateCurrentIndex(index: Int) {
        val oldIndex = _currentIndex.value
        if (index == oldIndex) return

        val videos = _videoList.value
        if (index < 0 || index >= videos.size) { // 增加索引越界检查
            // 如果索引越界（例如列表末尾且尝试播放下一个），可以考虑重置到第一个或不操作
            // 当前行为：如果开启连播且到末尾，则重置到第一个；否则不操作
            if (_autoPlayNextEnabled.value && index >= videos.size && videos.isNotEmpty()) {
                updateCurrentIndexInternal(0) // 连播到末尾，从头开始
            } else {
                 // 如果不是连播到末尾的情况，或者列表为空，则不进行无效的索引更新
                if (videos.isEmpty()) _currentIndex.value = 0 // 特殊处理空列表后加载的情况
            }
            return
        }
        
        updateCurrentIndexInternal(index)
    }

    private fun updateCurrentIndexInternal(index: Int) {
        val oldIndex = _currentIndex.value
        // 视频列表和ID获取逻辑移到这里，确保 index 有效
        val videos = _videoList.value
        val videoId = videos.getOrNull(index)?.video_id ?: return

        // 停止旧页面的播放器
        mediaPlayerService.stopPlayerForPage(oldIndex)

        // 更新当前索引和视频ID
        _currentIndex.value = index
        _currentVideoId.value = videoId

        // 初始化当前及相邻播放器
        if (index >= 0 && index < videos.size) {
            mediaPlayerService.initializePlayers(videos, index)
        }

        // 设置新页面为活跃状态和更新映射
        mediaPlayerService.updateActiveState(index, true)
        mediaPlayerService.updateMapping(videoId, index)
        mediaPlayerService.play(index) // 切换后自动播放新的视频

        // 检查是否需要加载更多视频
        checkLoadMore(index)
    }

    /**
     * 处理视频播放结束事件
     */
    private fun handleVideoEnded() {
        val currentIndexValue = _currentIndex.value
        if (_autoPlayNextEnabled.value) {
            // 连播开启：播放下一个视频
            val nextIndex = currentIndexValue + 1
            // updateCurrentIndex 会处理越界情况（如果到末尾则从头开始）
            updateCurrentIndex(nextIndex) 
        } else {
            // 连播关闭：重播当前视频
            mediaPlayerService.getPlayerByIndex(currentIndexValue)?.let {
                it.seekTo(0)
                it.play()
            }
        }
    }

}