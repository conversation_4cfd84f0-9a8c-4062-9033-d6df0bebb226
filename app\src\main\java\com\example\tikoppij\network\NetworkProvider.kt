package com.example.tikoppij.network

import com.example.tikoppij.model.VideoResponse
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.GET
import retrofit2.http.Query
import java.util.concurrent.TimeUnit

/**
 * 网络服务提供器
 * 整合OkHttp和Retrofit，提供应用全局共享的网络服务
 */
object NetworkProvider {
    // 基础API地址
    private const val BASE_URL = "http://ssb.yyywu.com/"
    
    /**
     * 视频API服务接口
     * 用于获取视频列表数据
     */
    interface ApiService {
        /**
         * 获取视频列表
         * @param category 分类ID
         * @param count 请求数量
         * @return 视频列表响应
         */
        @GET("v1/api/url")
        suspend fun getVideoList(
            @Query("c") category: Int = 99,
            @Query("r") count: Int = 10
        ): Response<VideoResponse>
    }
    
    /**
     * 获取用于视频加载的OkHttpClient
     * 针对大文件传输进行了优化
     */
    val videoHttpClient: OkHttpClient by lazy {
        createVideoHttpClient()
    }
    
    /**
     * 获取用于API请求的OkHttpClient
     */
    val apiHttpClient: OkHttpClient by lazy {
        createApiHttpClient()
    }
    
    /**
     * Retrofit API服务接口
     */
    val apiService: ApiService by lazy {
        createRetrofit().create(ApiService::class.java)
    }
    
    /**
     * 创建Retrofit实例
     */
    private fun createRetrofit(): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(apiHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    /**
     * 创建基础Builder的私有方法，封装通用配置
     */
    private fun createBaseHttpClientBuilder(logLevel: HttpLoggingInterceptor.Level): OkHttpClient.Builder {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = logLevel
        }
        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
    }
            
    /**
     * 创建视频客户端的私有方法
     */
    private fun createVideoHttpClient(): OkHttpClient {
        return createBaseHttpClientBuilder(HttpLoggingInterceptor.Level.NONE)
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .followRedirects(true)
            .followSslRedirects(true)
            .build()
    }

    /**
     * 创建API客户端的私有方法
     */
    private fun createApiHttpClient(): OkHttpClient {
        return createBaseHttpClientBuilder(HttpLoggingInterceptor.Level.NONE)
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .build()
    }
} 