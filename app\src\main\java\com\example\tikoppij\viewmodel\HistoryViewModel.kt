package com.example.tikoppij.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.MyApplication
import com.example.tikoppij.data.HistoryRepository
import com.example.tikoppij.initializer.AppInitializer
import com.example.tikoppij.model.HistoryModel
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.video.MediaPlayerService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 历史记录列表ViewModel
 * 管理观看历史的显示和操作
 */
@UnstableApi
class HistoryViewModel(application: Application) : AndroidViewModel(application) {
    
    // 历史记录管理仓库
    private val historyRepository: HistoryRepository = MyApplication.historyRepository
    
    // 播放器服务
    val mediaPlayerService: MediaPlayerService = AppInitializer.getMediaPlayerService(application)
    
    // 历史记录列表
    private val _historyList = MutableStateFlow<List<HistoryModel>>(emptyList())
    val historyList: StateFlow<List<HistoryModel>> = _historyList.asStateFlow()
    
    // 当前播放索引
    private val _currentIndex = MutableStateFlow(0)
    val currentIndex: StateFlow<Int> = _currentIndex.asStateFlow()
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    init {
        loadHistoryList()
    }
    
    /**
     * 加载历史记录列表
     */
    private fun loadHistoryList() {
        viewModelScope.launch {
            _isLoading.value = true
            historyRepository.getHistoryList().collectLatest { history ->
                _historyList.value = history // 已经按观看时间倒序排列
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 播放历史记录中的视频
     */
    fun playHistoryVideo(index: Int) {
        if (index >= 0 && index < _historyList.value.size) {
            _currentIndex.value = index
            val historyVideo = _historyList.value[index]
            val videoModel = historyVideo.toVideoModel()
            
            // 更新历史记录（移动到最前面）
            viewModelScope.launch {
                historyRepository.addHistory(videoModel, historyVideo.watchProgress)
            }
        }
    }
    
    /**
     * 从历史记录中移除视频
     */
    fun removeHistory(videoId: String) {
        viewModelScope.launch {
            historyRepository.removeHistory(videoId)
        }
    }
    
    /**
     * 清空历史记录
     */
    fun clearHistory() {
        viewModelScope.launch {
            historyRepository.clearHistory()
        }
    }
    
    /**
     * 更新当前播放索引
     */
    fun updateCurrentIndex(index: Int) {
        _currentIndex.value = index
    }
    
    /**
     * 获取当前历史视频列表作为VideoModel列表（用于播放）
     */
    fun getHistoryVideosAsVideoModels(): List<VideoModel> {
        return _historyList.value.map { it.toVideoModel() }
    }
    
    /**
     * 更新观看进度
     */
    fun updateWatchProgress(videoId: String, progress: Long) {
        viewModelScope.launch {
            historyRepository.updateWatchProgress(videoId, progress)
        }
    }
    
    /**
     * 格式化观看时间
     */
    fun formatWatchTime(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < 60 * 1000 -> "刚刚"
            diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
            diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
            diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)}天前"
            else -> "很久以前"
        }
    }
    
    /**
     * 格式化观看进度
     */
    fun formatWatchProgress(progress: Long): String {
        if (progress <= 0) return ""
        
        val minutes = progress / (60 * 1000)
        val seconds = (progress % (60 * 1000)) / 1000
        
        return if (minutes > 0) {
            "${minutes}分${seconds}秒"
        } else {
            "${seconds}秒"
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        // 停止当前页面的播放
        if (_historyList.value.isNotEmpty() && _currentIndex.value < _historyList.value.size) {
            mediaPlayerService.stopPlayerForPage(_currentIndex.value)
        }
    }
} 