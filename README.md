# Tikoppij - 现代化Android视频播放应用

## 📱 项目简介

Tikoppij是一个基于Android平台开发的现代化视频播放应用，采用最新的Kotlin编程语言和Jetpack Compose UI框架构建。应用提供流畅的视频播放体验，支持网络视频流播放、本地缓存管理、多页面导航等功能。

## ✨ 核心特性

### 🎥 视频播放
- **高性能播放引擎**: 基于Android Media3 ExoPlayer实现
- **播放器池管理**: 智能的播放器资源管理和复用
- **网络流播放**: 支持在线视频流播放
- **播放控制**: 完整的播放、暂停、进度控制功能

### 🏗️ 应用架构
- **现代UI**: 采用Jetpack Compose构建响应式用户界面
- **MVVM架构**: 使用ViewModel进行状态管理
- **导航系统**: Compose Navigation实现页面导航
- **主题系统**: Material3设计风格，支持深色/浅色主题

### 💾 缓存管理
- **智能缓存**: 视频缓存管理系统
- **存储优化**: 缓存空间监控和清理功能
- **性能监控**: 内置性能监控工具

### 🌐 网络功能
- **HTTP客户端**: 基于Retrofit + OkHttp的网络请求
- **数据解析**: Gson序列化/反序列化
- **网络监控**: 请求日志和性能监控

## 🛠️ 技术栈

### 核心框架
- **Kotlin**: 主要编程语言
- **Jetpack Compose**: 现代声明式UI框架
- **Android Gradle Plugin**: 构建系统

### 依赖库
```kotlin
// UI & 导航
- Jetpack Compose (BOM)
- Material3
- Navigation Compose
- Activity Compose

// 媒体播放
- Media3 ExoPlayer
- Media3 Common
- Media3 DataSource OkHttp

// 网络请求
- Retrofit
- OkHttp
- Gson Converter
- Logging Interceptor

// 异步处理
- Kotlin Coroutines
- Lifecycle Runtime Compose
- ViewModel Compose

// 数据存储
- DataStore Preferences

// 应用启动
- App Startup Runtime
```

## 📁 项目结构

```
app/src/main/java/com/example/tikoppij/
├── 📁 data/              # 数据层
├── 📁 initializer/       # 应用初始化
├── 📁 model/            # 数据模型
├── 📁 network/          # 网络服务
│   └── NetworkProvider.kt
├── 📁 ui/               # 用户界面
│   ├── 📁 components/   # UI组件
│   ├── 📁 navigation/   # 导航配置
│   ├── 📁 screens/      # 页面组件
│   │   ├── VideoScreen.kt        # 视频播放页面
│   │   ├── ProfileScreen.kt      # 个人中心页面
│   │   ├── ToolsScreen.kt        # 工具页面
│   │   ├── CacheManagementScreen.kt # 缓存管理页面
│   │   └── HiddenScreen.kt       # 隐藏功能页面
│   └── 📁 theme/        # 主题配置
├── 📁 utils/            # 工具类
├── 📁 video/            # 视频功能
│   ├── CacheManager.kt          # 缓存管理器
│   ├── MediaPlayerService.kt    # 媒体播放服务
│   ├── PlayerPoolManager.kt     # 播放器池管理
│   └── VideoIndexMapper.kt      # 视频索引映射
├── 📁 viewmodel/        # 视图模型
├── MainActivity.kt      # 主活动
└── MyApplication.kt     # 应用程序类
```

## 🚀 功能模块

### 主要页面
1. **视频播放页面** (`VideoScreen.kt`)
   - 视频播放界面
   - 播放控制组件
   - 全屏播放支持

2. **个人中心页面** (`ProfileScreen.kt`)
   - 用户信息展示
   - 设置和配置选项

3. **工具页面** (`ToolsScreen.kt`)
   - 应用工具集合
   - 功能快捷入口

4. **缓存管理页面** (`CacheManagementScreen.kt`)
   - 缓存统计信息
   - 缓存清理功能
   - 存储空间管理

### 核心服务
- **播放器池管理**: 优化播放器资源使用
- **缓存管理**: 智能视频缓存策略
- **网络服务**: 统一的网络请求管理
- **性能监控**: 应用性能指标收集

## 🔧 开发配置

### 编译要求
- **compileSdk**: 35
- **minSdk**: 24
- **targetSdk**: 35
- **Java版本**: 11

### 权限配置
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

### 应用配置
- **包名**: `com.example.tikoppij`
- **应用ID**: `com.example.tikoppij4`
- **版本**: 1.0 (versionCode: 1)

## 📋 开发特性

### UI/UX设计
- **Edge-to-Edge**: 沉浸式全屏体验
- **动态主题**: 自适应系统主题
- **流畅动画**: Compose动画系统
- **响应式布局**: 适配不同屏幕尺寸

### 性能优化
- **屏幕常亮**: 视频播放时保持屏幕亮度
- **内存管理**: 播放器资源池化管理
- **网络优化**: 智能缓存和预加载
- **启动优化**: App Startup快速启动

### 用户体验
- **返回键处理**: 双击退出应用
- **状态栏控制**: 动态显示/隐藏状态栏
- **导航抽屉**: 侧滑导航菜单
- **底部导航**: 主要功能快速切换

## 🔄 版本信息

- **当前版本**: 1.0
- **最低Android版本**: Android 7.0 (API 24)
- **目标Android版本**: Android 14 (API 35)

## 📄 许可证

本项目仅供学习和研究使用。

---

*构建现代化的Android视频播放体验 🎬* 