package com.example.tikoppij.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.MyApplication
import com.example.tikoppij.data.FavoriteRepository
import com.example.tikoppij.data.HistoryRepository
import com.example.tikoppij.initializer.AppInitializer
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.ui.components.VideoDisplayMode
import com.example.tikoppij.video.MediaPlayerService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 独立播放页面ViewModel
 * 专门用于收藏和历史列表的视频播放，避免与主页面ViewModel冲突
 */
@UnstableApi
class VideoPlayerViewModel(application: Application) : AndroidViewModel(application) {
    
    // 收藏管理仓库
    private val favoriteRepository: FavoriteRepository = MyApplication.favoriteRepository
    
    // 历史记录管理仓库
    private val historyRepository: HistoryRepository = MyApplication.historyRepository
    
    // 用户偏好设置仓库
    private val userPreferencesRepository = MyApplication.userPreferencesRepository

    // 播放器服务 - 创建独立实例
    val mediaPlayerService: MediaPlayerService = AppInitializer.getMediaPlayerService(application)
    
    // 视频列表
    private val _videoList = MutableStateFlow<List<VideoModel>>(emptyList())
    val videoList: StateFlow<List<VideoModel>> = _videoList.asStateFlow()

    // 当前播放索引
    private val _currentIndex = MutableStateFlow(0)
    val currentIndex: StateFlow<Int> = _currentIndex.asStateFlow()

    // 当前视频的收藏状态
    private val _currentVideoIsFavorite = MutableStateFlow(false)
    val currentVideoIsFavorite: StateFlow<Boolean> = _currentVideoIsFavorite.asStateFlow()
    
    // 视频显示模式
    private val _videoDisplayMode = MutableStateFlow(VideoDisplayMode.AUTO_ADAPT)
    val videoDisplayMode: StateFlow<VideoDisplayMode> = _videoDisplayMode.asStateFlow()

    // 自动播放下一个视频的设置
    private val _autoPlayNextEnabled = MutableStateFlow(false)
    val autoPlayNextEnabled: StateFlow<Boolean> = _autoPlayNextEnabled.asStateFlow()

    // 收藏状态监听Job
    private var favoriteStatusJob: kotlinx.coroutines.Job? = null
    
    // 保存原始的onVideoEnded回调
    private var originalOnVideoEnded: ((pageIndex: Int) -> Unit)? = null

    init {
        // 加载显示模式设置
        viewModelScope.launch {
            userPreferencesRepository.videoDisplayMode.collectLatest { mode ->
                _videoDisplayMode.value = mode
            }
        }
        
        // 加载自动播放下一个视频设置
        viewModelScope.launch {
            userPreferencesRepository.autoPlayNextEnabled.collectLatest { enabled ->
                _autoPlayNextEnabled.value = enabled
            }
        }
        
        // 保存原始回调并设置新的回调
        originalOnVideoEnded = mediaPlayerService.onVideoEnded
        mediaPlayerService.onVideoEnded = { pageIndex ->
            handleVideoEnded(pageIndex)
        }
    }

    /**
     * 处理视频播放结束事件
     */
    private fun handleVideoEnded(pageIndex: Int) {
        // 只处理当前页面的视频结束事件
        if (pageIndex == _currentIndex.value) {
            val videos = _videoList.value
            val nextIndex = pageIndex + 1
            
            if (_autoPlayNextEnabled.value && nextIndex < videos.size) {
                // 自动播放下一个视频
                updateCurrentIndex(nextIndex)
            } else {
                // 到达列表末尾或自动播放关闭，重播当前视频
                mediaPlayerService.getPlayerByIndex(pageIndex)?.let { player ->
                    player.seekTo(0)
                    player.play()
                }
            }
        }
    }

    /**
     * 简化的播放器初始化 - 只初始化播放器，不管理索引状态
     */
    fun initializePlayersOnly(videoList: List<VideoModel>, startIndex: Int) {
        viewModelScope.launch {
            if (videoList.isNotEmpty() && startIndex >= 0 && startIndex < videoList.size) {
                android.util.Log.d("PlayerDebug", "=== 简化播放器初始化 ===")
                android.util.Log.d("PlayerDebug", "视频列表大小: ${videoList.size}")
                android.util.Log.d("PlayerDebug", "起始索引: $startIndex")
                android.util.Log.d("PlayerDebug", "起始视频ID: ${videoList[startIndex].video_id}")

                // 清理所有播放器槽位
                mediaPlayerService.clearAllSlots()

                // 更新视频列表（不更新currentIndex）
                _videoList.value = videoList

                // 设置映射关系
                mediaPlayerService.updateMappingBatch(0, videoList.map { it.video_id })

                // 初始化播放器
                mediaPlayerService.initializePlayers(videoList, startIndex)

                // 开始播放指定索引的视频
                mediaPlayerService.play(startIndex)

                android.util.Log.d("PlayerDebug", "简化初始化完成")
            }
        }
    }

    /**
     * 原始的初始化播放列表方法（保留兼容性）
     */
    fun initializePlaylist(videoList: List<VideoModel>, startIndex: Int) {
        initializePlayersOnly(videoList, startIndex)
    }

    /**
     * 更新当前播放索引
     */
    fun updateCurrentIndex(index: Int) {
        val videos = _videoList.value
        if (index >= 0 && index < videos.size && index != _currentIndex.value) {
            val oldIndex = _currentIndex.value
            
            // 停止旧播放器
            mediaPlayerService.stopPlayerForPage(oldIndex)
            
            // 更新索引
            _currentIndex.value = index
            
            // 更新收藏状态
            updateCurrentVideoFavoriteStatus(videos[index].video_id)
            
            // 记录历史
            addToHistory(videos[index])
            
            // 播放新视频
            mediaPlayerService.initializePlayers(videos, index)
            mediaPlayerService.play(index)
        }
    }

    /**
     * 切换收藏状态
     */
    fun toggleCurrentVideoFavorite() {
        viewModelScope.launch {
            val videos = _videoList.value
            if (videos.isNotEmpty() && _currentIndex.value < videos.size) {
                val currentVideo = videos[_currentIndex.value]
                
                if (_currentVideoIsFavorite.value) {
                    favoriteRepository.removeFavorite(currentVideo.video_id)
                } else {
                    favoriteRepository.addFavorite(currentVideo)
                }
            }
        }
    }

    /**
     * 更新收藏状态
     */
    fun updateCurrentVideoFavoriteStatus(videoId: String) {
        favoriteStatusJob?.cancel()
        favoriteStatusJob = viewModelScope.launch {
            favoriteRepository.isFavorite(videoId).collectLatest { isFavorite ->
                _currentVideoIsFavorite.value = isFavorite
            }
        }
    }

    /**
     * 添加到历史记录
     */
    fun addToHistory(video: VideoModel) {
        viewModelScope.launch {
            historyRepository.addHistory(video)
        }
    }

    /**
     * 切换视频显示模式
     */
    fun toggleVideoDisplayMode() {
        viewModelScope.launch {
            val currentMode = _videoDisplayMode.value
            val newMode = when (currentMode) {
                VideoDisplayMode.AUTO_ADAPT -> VideoDisplayMode.FIT
                VideoDisplayMode.FIT -> VideoDisplayMode.AUTO_ADAPT
            }
            userPreferencesRepository.updateVideoDisplayMode(newMode)
        }
    }

    /**
     * 切换自动播放下一个视频设置
     */
    fun toggleAutoPlayNextEnabled() {
        val newValue = !_autoPlayNextEnabled.value
        viewModelScope.launch {
            userPreferencesRepository.updateAutoPlayNextEnabled(newValue)
        }
    }

    override fun onCleared() {
        super.onCleared()
        favoriteStatusJob?.cancel()
        // 停止播放器
        mediaPlayerService.stopPlayerForPage(_currentIndex.value)
        // 恢复原始回调
        mediaPlayerService.onVideoEnded = originalOnVideoEnded
    }
} 