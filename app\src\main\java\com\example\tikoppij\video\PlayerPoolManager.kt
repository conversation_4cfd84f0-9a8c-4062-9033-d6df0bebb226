package com.example.tikoppij.video

import android.content.Context
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.DefaultRenderersFactory
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.upstream.DefaultAllocator
import com.example.tikoppij.model.VideoModel
import java.util.concurrent.ConcurrentHashMap

/**
 * 播放器池管理器
 * 负责管理ExoPlayer实例池和播放状态
 */
@UnstableApi
class PlayerPoolManager(
    private val context: Context,
    private val cacheManager: CacheManager
) {
    // 固定大小为3的播放器槽位
    private val playerSlotCount = 3
    private val playerSlots = arrayOfNulls<PlayerState>(playerSlotCount)
    
    // 保存每个页面的播放状态（true=播放，false=暂停）
    private val playbackStateMap = ConcurrentHashMap<Int, Boolean>()
    
    // 视频结束回调
    var onVideoEnded: ((pageIndex: Int) -> Unit)? = null
    
    private data class PlayerState(
        val player: ExoPlayer,
        var videoId: String? = null,
        var pageIndex: Int = -1,
        var isActive: Boolean = false
    )
    
    /**
     * 获取或创建视频播放器
     * @param video 视频数据
     * @param pageIndex 视频在列表中的实际索引位置
     * @return ExoPlayer实例
     */
    fun getOrCreatePlayer(video: VideoModel, pageIndex: Int): ExoPlayer {
        if (pageIndex < 0) {
            throw IllegalArgumentException("Page index must be non-negative for player retrieval.")
        }

        android.util.Log.d("SlotDebug", "=== 重构版获取播放器 ===")
        android.util.Log.d("SlotDebug", "请求页面: $pageIndex, 视频ID: ${video.video_id}")

        val slotIndex = getSlotIndexForPage(pageIndex)
        val existingState = playerSlots[slotIndex]

        if (existingState != null) {
            // 检查是否是同一个页面
            if (existingState.pageIndex == pageIndex) {
                android.util.Log.d("SlotDebug", "复用现有播放器，槽位$slotIndex")

                // 检查视频是否需要更新
                if (existingState.videoId != video.video_id) {
                    android.util.Log.d("SlotDebug", "更新视频内容: ${existingState.videoId} -> ${video.video_id}")
                    updatePlayerContent(existingState.player, video)
                    existingState.videoId = video.video_id
                }

                existingState.isActive = true
                return existingState.player
            } else {
                // 槽位被其他页面占用，需要清理
                android.util.Log.d("SlotDebug", "清理槽位$slotIndex (原页面${existingState.pageIndex} -> 新页面$pageIndex)")
                existingState.player.stop()
                existingState.player.release()
            }
        }

        // 创建新播放器
        android.util.Log.d("SlotDebug", "创建新播放器，槽位$slotIndex，页面$pageIndex")
        val newPlayer = createPlayer(video)
        playerSlots[slotIndex] = PlayerState(
            player = newPlayer,
            videoId = video.video_id,
            pageIndex = pageIndex,
            isActive = true
        )

        return newPlayer
    }

    /**
     * 更新播放器内容
     */
    private fun updatePlayerContent(player: ExoPlayer, video: VideoModel) {
        player.stop()
        player.clearMediaItems()
        player.setMediaItem(MediaItem.Builder()
            .setUri(video.url)
            .setMediaId(video.video_id)
            .build())
        player.prepare()

        if (cacheManager.isCachedForPlayback(video.url)) {
            player.seekTo(0)
            player.playWhenReady = false
        }
    }
    
    /**
     * 创建播放器
     * @param video 视频数据
     * @return 播放器实例
     */
    private fun createPlayer(video: VideoModel): ExoPlayer {
        // 获取所需组件
        val isCached = cacheManager.isCachedForPlayback(video.url)
        val cacheDataSourceFactory = cacheManager.getCacheDataSourceFactory()
        val mediaSourceFactory = DefaultMediaSourceFactory(cacheDataSourceFactory)
        
        // 创建自定义加载控制器，优化缓冲策略
        val loadControl = DefaultLoadControl.Builder()
            .setAllocator(DefaultAllocator(true, 8 * 1024 * 1024))
            .setBufferDurationsMs(
                2000,  // minBufferMs
                50000, // maxBufferMs
                1000,  // bufferForPlaybackMs
                2000   // bufferForPlaybackAfterRebufferMs
            )
            .setPrioritizeTimeOverSizeThresholds(true)
            .build()
        
        // 创建硬件加速优先的渲染器工厂
        val renderersFactory = DefaultRenderersFactory(context)
            .setExtensionRendererMode(DefaultRenderersFactory.EXTENSION_RENDERER_MODE_PREFER)
            .experimentalSetEnableMediaCodecVideoRendererPrewarming(true) // 启用预热
        
        // 创建播放器
        val player = ExoPlayer.Builder(context)
            .setRenderersFactory(renderersFactory)
            .setMediaSourceFactory(mediaSourceFactory)
            .setLoadControl(loadControl)
            .setReleaseTimeoutMs(5000)
            .build()
        
        // 设置初始播放状态
        player.playWhenReady = false
        
        // 设置循环播放
        player.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(state: Int) {
                if (state == Player.STATE_ENDED) {
                    // 当视频播放结束时，调用回调函数，而不是直接重播
                    val playerState = playerSlots.find { it?.player == player }
                    playerState?.pageIndex?.let { pageIndex ->
                        onVideoEnded?.invoke(pageIndex)
                    }
                }
            }
        })
        
        // 设置媒体项
        player.setMediaItem(MediaItem.Builder()
            .setUri(video.url)
            .setMediaId(video.video_id)
            .build())
        
        // 准备播放器
        player.prepare()
        
        // 如果已缓存，设置播放位置为0
        if (isCached) {
            player.seekTo(0)
        }
        
        return player
    }
    
    /**
     * 播放视频
     * @param pageIndex 页面索引
     */
    fun play(pageIndex: Int) {
        if (pageIndex < 0) return

        // 直接查找对应页面的播放器
        playerSlots.forEach { state ->
            if (state?.pageIndex == pageIndex) {
                android.util.Log.d("SlotDebug", "播放页面$pageIndex")
                state.player.playWhenReady = true
                state.player.play()
                state.isActive = true
                return
            }
        }

        android.util.Log.w("SlotDebug", "未找到页面$pageIndex 的播放器")
    }
    
    /**
     * 暂停视频
     * @param pageIndex 页面索引
     * @param updateActiveStatus 是否更新活跃状态
     */
    fun pause(pageIndex: Int, updateActiveStatus: Boolean = true) {
        if (pageIndex < 0) return

        // 直接查找对应页面的播放器
        playerSlots.forEach { state ->
            if (state?.pageIndex == pageIndex) {
                android.util.Log.d("SlotDebug", "暂停页面$pageIndex")
                state.player.playWhenReady = false

                if (state.player.isPlaying) {
                    state.player.pause()
                }

                if (updateActiveStatus) {
                    state.isActive = false
                }
                return
            }
        }

        android.util.Log.w("SlotDebug", "未找到页面$pageIndex 的播放器")
    }
    
    /**
     * 根据页面索引获取槽位索引
     */
    /**
     * 重构的槽位分配逻辑
     * 使用LRU（最近最少使用）策略来管理槽位
     */
    private fun getSlotIndexForPage(pageIndex: Int): Int {
        // 首先检查是否已经有槽位分配给这个页面
        playerSlots.forEachIndexed { slotIndex, state ->
            if (state?.pageIndex == pageIndex) {
                android.util.Log.d("SlotDebug", "页面$pageIndex 已有槽位$slotIndex")
                return slotIndex
            }
        }

        // 寻找空闲槽位
        playerSlots.forEachIndexed { slotIndex, state ->
            if (state == null) {
                android.util.Log.d("SlotDebug", "页面$pageIndex 分配到空闲槽位$slotIndex")
                return slotIndex
            }
        }

        // 所有槽位都被占用，使用LRU策略找到最久未使用的槽位
        var oldestSlot = 0
        var oldestTime = Long.MAX_VALUE

        playerSlots.forEachIndexed { slotIndex, state ->
            if (state != null && !state.isActive) {
                // 找到最久未使用的非活跃槽位
                oldestSlot = slotIndex
                oldestTime = 0 // 简化实现，直接使用第一个非活跃槽位
                android.util.Log.d("SlotDebug", "页面$pageIndex 将复用非活跃槽位$slotIndex (原页面${state.pageIndex})")
                return slotIndex
            }
        }

        // 如果所有槽位都是活跃的，使用简单的轮询策略
        val fallbackSlot = pageIndex % playerSlotCount
        android.util.Log.d("SlotDebug", "页面$pageIndex 使用轮询槽位$fallbackSlot")
        return fallbackSlot
    }
    
    /**
     * 初始化播放器
     * 预先为当前和相邻槽位的播放器准备实例
     * @param videoList 视频列表
     * @param currentIndex 当前页面索引
     */
    fun initializePlayers(videoList: List<VideoModel>, currentIndex: Int = 0) {
        if (videoList.isEmpty()) return

        // 确保当前索引有效
        val validCurrentIndex = currentIndex.coerceIn(videoList.indices)

        // 计算需要初始化的页面索引 (当前页，前一页，后一页)
        val indicesToPrepare = listOf(
            validCurrentIndex,
            (validCurrentIndex - 1).coerceAtLeast(0),
            (validCurrentIndex + 1).coerceAtMost(videoList.size - 1)
        ).distinct() // 去重，避免列表太短时重复

        // 准备播放器实例
        indicesToPrepare.forEach { pageIndex ->
            try {
                val video = videoList.getOrNull(pageIndex) ?: return@forEach
                getOrCreatePlayer(video, pageIndex)
            } catch (_: Exception) {
                // 忽略异常
            }
        }
    }
    
    /**
     * 更新播放器活跃状态
     * @param pageIndex 页面索引
     * @param isActive 是否活跃
     */
    fun updateActiveState(pageIndex: Int, isActive: Boolean) {
        if (pageIndex < 0) return

        val slotIndex = getSlotIndexForPage(pageIndex)
        playerSlots[slotIndex]?.let { state ->
            if (state.pageIndex == pageIndex) {
                state.isActive = isActive
            }
        }
    }
    
    /**
     * 停止指定页面的播放器
     * @param pageIndex 页面索引
     */
    fun stopPlayerForPage(pageIndex: Int) {
        // 直接调用pause方法并设置updateActiveStatus为true，避免重复逻辑
        pause(pageIndex, true)
    }
    
    /**
     * 根据页面索引获取播放器
     * @param pageIndex 页面索引
     * @return 播放器实例，如果不存在则返回null
     */
    fun getPlayerByIndex(pageIndex: Int): ExoPlayer? {
        if (pageIndex < 0) return null

        val slotIndex = getSlotIndexForPage(pageIndex)
        return playerSlots[slotIndex]?.let { state ->
            if (state.pageIndex == pageIndex) {
                state.player
            } else {
                null
            }
        }
    }

    /**
     * 保存指定页面的播放状态
     * @param pageIndex 页面索引
     * @param isPlaying 是否正在播放
     */
    fun savePlaybackState(pageIndex: Int, isPlaying: Boolean) {
        if (pageIndex >= 0) {
            playbackStateMap[pageIndex] = isPlaying
        }
    }
    
    /**
     * 获取指定页面的播放状态
     * @param pageIndex 页面索引
     * @return 播放状态，如果不存在则返回null
     */
    fun getPlaybackState(pageIndex: Int): Boolean? {
        return if (pageIndex >= 0) {
            playbackStateMap[pageIndex]
        } else {
            null
        }
    }

    /**
     * 清理所有播放器槽位
     * 用于重新初始化播放列表时清理可能的槽位冲突
     */
    fun clearAllSlots() {
        playerSlots.forEachIndexed { index, state ->
            state?.let {
                it.player.stop()
                it.player.release()
                playerSlots[index] = null
            }
        }
        playbackStateMap.clear()
    }

}