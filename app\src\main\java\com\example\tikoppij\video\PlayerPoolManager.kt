package com.example.tikoppij.video

import android.content.Context
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.DefaultRenderersFactory
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.upstream.DefaultAllocator
import com.example.tikoppij.model.VideoModel
import java.util.concurrent.ConcurrentHashMap

/**
 * 播放器池管理器
 * 负责管理ExoPlayer实例池和播放状态
 */
@UnstableApi
class PlayerPoolManager(
    private val context: Context,
    private val cacheManager: CacheManager
) {
    // 固定大小为3的播放器槽位
    private val playerSlotCount = 3
    private val playerSlots = arrayOfNulls<PlayerState>(playerSlotCount)
    
    // 保存每个页面的播放状态（true=播放，false=暂停）
    private val playbackStateMap = ConcurrentHashMap<Int, Boolean>()
    
    // 视频结束回调
    var onVideoEnded: ((pageIndex: Int) -> Unit)? = null
    
    private data class PlayerState(
        val player: ExoPlayer,
        var videoId: String? = null,
        var pageIndex: Int = -1,
        var isActive: Boolean = false
    )
    
    /**
     * 获取或创建视频播放器
     * @param video 视频数据
     * @param pageIndex 视频在列表中的实际索引位置
     * @return ExoPlayer实例
     */
    fun getOrCreatePlayer(video: VideoModel, pageIndex: Int): ExoPlayer {
        if (pageIndex < 0) {
            throw IllegalArgumentException("Page index must be non-negative for player retrieval.")
        }
        val slotIndex = getSlotIndexForPage(pageIndex)

        // 调试日志：槽位分配信息
        android.util.Log.d("SlotDebug", "=== 获取播放器 ===")
        android.util.Log.d("SlotDebug", "请求页面索引: $pageIndex")
        android.util.Log.d("SlotDebug", "请求视频ID: ${video.video_id}")
        android.util.Log.d("SlotDebug", "分配槽位: $slotIndex")

        // 获取或创建指定槽位的播放器
        val existingState = playerSlots[slotIndex]

        if (existingState != null) {
            android.util.Log.d("SlotDebug", "槽位$slotIndex 已有播放器，当前页面索引: ${existingState.pageIndex}")
            android.util.Log.d("SlotDebug", "槽位$slotIndex 当前视频ID: ${existingState.videoId}")

            // 检查槽位是否被正确的页面占用
            if (existingState.pageIndex == pageIndex) {
                android.util.Log.d("SlotDebug", "槽位匹配正确，复用播放器")
                // 槽位正确，检查视频内容是否需要更新
                val existingPlayer = existingState.player
                if (existingPlayer.currentMediaItem?.mediaId != video.video_id) {
                    // 重置播放器状态
                    existingPlayer.stop()
                    existingPlayer.clearMediaItems()

                    // 设置新的媒体项
                    existingPlayer.apply {
                        setMediaItem(MediaItem.Builder()
                            .setUri(video.url)
                            .setMediaId(video.video_id)
                            .build())
                        prepare() // 准备新的媒体项

                        // 如果已缓存，设置播放位置为0
                        if (cacheManager.isCachedForPlayback(video.url)) {
                            seekTo(0)
                            playWhenReady = false
                        }
                    }

                    // 更新视频ID
                    existingState.videoId = video.video_id
                }

                existingState.isActive = true
                return existingPlayer
            } else {
                android.util.Log.w("SlotDebug", "槽位冲突！槽位$slotIndex 被页面${existingState.pageIndex}占用，但请求的是页面$pageIndex")
                android.util.Log.d("SlotDebug", "清理槽位并重新创建播放器")

                // 槽位被其他页面占用，需要清理并重新创建
                existingState.player.stop()
                existingState.player.release()

                // 创建新播放器并占用槽位
                val newPlayer = createPlayer(video)
                playerSlots[slotIndex] = PlayerState(
                    player = newPlayer,
                    videoId = video.video_id,
                    pageIndex = pageIndex,
                    isActive = true
                )
                android.util.Log.d("SlotDebug", "槽位$slotIndex 重新分配给页面$pageIndex，视频ID: ${video.video_id}")
                return newPlayer
            }
        } else {
            android.util.Log.d("SlotDebug", "槽位$slotIndex 为空，创建新播放器")

            // 槽位为空，创建新播放器
            val newPlayer = createPlayer(video)

            // 添加到指定槽位
            playerSlots[slotIndex] = PlayerState(
                player = newPlayer,
                videoId = video.video_id,
                pageIndex = pageIndex,
                isActive = true
            )

            android.util.Log.d("SlotDebug", "槽位$slotIndex 分配给页面$pageIndex，视频ID: ${video.video_id}")
            return newPlayer
        }
    }
    
    /**
     * 创建播放器
     * @param video 视频数据
     * @return 播放器实例
     */
    private fun createPlayer(video: VideoModel): ExoPlayer {
        // 获取所需组件
        val isCached = cacheManager.isCachedForPlayback(video.url)
        val cacheDataSourceFactory = cacheManager.getCacheDataSourceFactory()
        val mediaSourceFactory = DefaultMediaSourceFactory(cacheDataSourceFactory)
        
        // 创建自定义加载控制器，优化缓冲策略
        val loadControl = DefaultLoadControl.Builder()
            .setAllocator(DefaultAllocator(true, 8 * 1024 * 1024))
            .setBufferDurationsMs(
                2000,  // minBufferMs
                50000, // maxBufferMs
                1000,  // bufferForPlaybackMs
                2000   // bufferForPlaybackAfterRebufferMs
            )
            .setPrioritizeTimeOverSizeThresholds(true)
            .build()
        
        // 创建硬件加速优先的渲染器工厂
        val renderersFactory = DefaultRenderersFactory(context)
            .setExtensionRendererMode(DefaultRenderersFactory.EXTENSION_RENDERER_MODE_PREFER)
            .experimentalSetEnableMediaCodecVideoRendererPrewarming(true) // 启用预热
        
        // 创建播放器
        val player = ExoPlayer.Builder(context)
            .setRenderersFactory(renderersFactory)
            .setMediaSourceFactory(mediaSourceFactory)
            .setLoadControl(loadControl)
            .setReleaseTimeoutMs(5000)
            .build()
        
        // 设置初始播放状态
        player.playWhenReady = false
        
        // 设置循环播放
        player.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(state: Int) {
                if (state == Player.STATE_ENDED) {
                    // 当视频播放结束时，调用回调函数，而不是直接重播
                    val playerState = playerSlots.find { it?.player == player }
                    playerState?.pageIndex?.let { pageIndex ->
                        onVideoEnded?.invoke(pageIndex)
                    }
                }
            }
        })
        
        // 设置媒体项
        player.setMediaItem(MediaItem.Builder()
            .setUri(video.url)
            .setMediaId(video.video_id)
            .build())
        
        // 准备播放器
        player.prepare()
        
        // 如果已缓存，设置播放位置为0
        if (isCached) {
            player.seekTo(0)
        }
        
        return player
    }
    
    /**
     * 播放视频
     * @param pageIndex 页面索引
     */
    fun play(pageIndex: Int) {
        if (pageIndex < 0) return

        val slotIndex = getSlotIndexForPage(pageIndex)
        playerSlots[slotIndex]?.let { state ->
            if (state.pageIndex == pageIndex) {
                state.player.playWhenReady = true
                state.player.play()
                state.isActive = true
            }
        }
    }
    
    /**
     * 暂停视频
     * @param pageIndex 页面索引
     * @param updateActiveStatus 是否更新活跃状态
     */
    fun pause(pageIndex: Int, updateActiveStatus: Boolean = true) {
        if (pageIndex < 0) return

        val slotIndex = getSlotIndexForPage(pageIndex)
        playerSlots[slotIndex]?.let { state ->
            if (state.pageIndex == pageIndex) {
                // 立即设置playWhenReady为false，这是暂停的关键标志
                state.player.playWhenReady = false

                // 确保调用pause，防止播放器状态不一致
                if (state.player.isPlaying) {
                    state.player.pause()
                }

                // 有条件地更新活跃状态（用于UI控制）
                if (updateActiveStatus) {
                    state.isActive = false
                }
            }
        }
    }
    
    /**
     * 根据页面索引获取槽位索引
     */
    private fun getSlotIndexForPage(pageIndex: Int): Int {
        return pageIndex.coerceAtLeast(0) % playerSlotCount
    }
    
    /**
     * 初始化播放器
     * 预先为当前和相邻槽位的播放器准备实例
     * @param videoList 视频列表
     * @param currentIndex 当前页面索引
     */
    fun initializePlayers(videoList: List<VideoModel>, currentIndex: Int = 0) {
        if (videoList.isEmpty()) return

        // 确保当前索引有效
        val validCurrentIndex = currentIndex.coerceIn(videoList.indices)

        // 计算需要初始化的页面索引 (当前页，前一页，后一页)
        val indicesToPrepare = listOf(
            validCurrentIndex,
            (validCurrentIndex - 1).coerceAtLeast(0),
            (validCurrentIndex + 1).coerceAtMost(videoList.size - 1)
        ).distinct() // 去重，避免列表太短时重复

        // 准备播放器实例
        indicesToPrepare.forEach { pageIndex ->
            try {
                val video = videoList.getOrNull(pageIndex) ?: return@forEach
                getOrCreatePlayer(video, pageIndex)
            } catch (_: Exception) {
                // 忽略异常
            }
        }
    }
    
    /**
     * 更新播放器活跃状态
     * @param pageIndex 页面索引
     * @param isActive 是否活跃
     */
    fun updateActiveState(pageIndex: Int, isActive: Boolean) {
        if (pageIndex < 0) return

        val slotIndex = getSlotIndexForPage(pageIndex)
        playerSlots[slotIndex]?.let { state ->
            if (state.pageIndex == pageIndex) {
                state.isActive = isActive
            }
        }
    }
    
    /**
     * 停止指定页面的播放器
     * @param pageIndex 页面索引
     */
    fun stopPlayerForPage(pageIndex: Int) {
        // 直接调用pause方法并设置updateActiveStatus为true，避免重复逻辑
        pause(pageIndex, true)
    }
    
    /**
     * 根据页面索引获取播放器
     * @param pageIndex 页面索引
     * @return 播放器实例，如果不存在则返回null
     */
    fun getPlayerByIndex(pageIndex: Int): ExoPlayer? {
        if (pageIndex < 0) return null

        val slotIndex = getSlotIndexForPage(pageIndex)
        return playerSlots[slotIndex]?.let { state ->
            if (state.pageIndex == pageIndex) {
                state.player
            } else {
                null
            }
        }
    }

    /**
     * 保存指定页面的播放状态
     * @param pageIndex 页面索引
     * @param isPlaying 是否正在播放
     */
    fun savePlaybackState(pageIndex: Int, isPlaying: Boolean) {
        if (pageIndex >= 0) {
            playbackStateMap[pageIndex] = isPlaying
        }
    }
    
    /**
     * 获取指定页面的播放状态
     * @param pageIndex 页面索引
     * @return 播放状态，如果不存在则返回null
     */
    fun getPlaybackState(pageIndex: Int): Boolean? {
        return if (pageIndex >= 0) {
            playbackStateMap[pageIndex]
        } else {
            null
        }
    }

    /**
     * 清理所有播放器槽位
     * 用于重新初始化播放列表时清理可能的槽位冲突
     */
    fun clearAllSlots() {
        playerSlots.forEachIndexed { index, state ->
            state?.let {
                it.player.stop()
                it.player.release()
                playerSlots[index] = null
            }
        }
        playbackStateMap.clear()
    }

}