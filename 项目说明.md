# Tikoppij 项目说明

## 项目概述

**Tikoppij** 是一个基于 Android 平台的现代化视频播放应用。项目采用最新的 Android 开发技术栈，提供流畅的视频观看体验。

## 主要功能

### 1. 视频播放
- 支持在线视频流播放
- 高性能的 ExoPlayer 播放引擎
- 流畅的播放控制和进度管理
- 支持全屏播放模式

### 2. 用户界面
- **首页**: 主要视频内容展示
- **工具页**: 应用功能工具集合
- **个人中心**: 用户设置和个人信息
- **缓存管理**: 视频缓存的查看和清理

### 3. 核心特色
- 🎨 **现代化设计**: 采用 Material3 设计规范
- 🚀 **高性能**: 播放器资源池化管理，优化内存使用
- 💾 **智能缓存**: 视频缓存系统，支持离线观看
- 🌙 **主题适配**: 自动适配系统深色/浅色主题
- 📱 **沉浸式体验**: Edge-to-Edge 全屏设计

## 技术架构

### 开发语言与框架
- **Kotlin**: 100% Kotlin 开发
- **Jetpack Compose**: 现代声明式UI框架
- **MVVM 架构**: 清晰的代码组织结构

### 主要依赖
- **Media3**: 专业的媒体播放解决方案
- **Retrofit**: 网络请求处理
- **Coroutines**: 异步编程
- **Navigation**: 页面导航管理
- **DataStore**: 数据持久化

## 项目结构

```
📦 主要模块
├── 🎬 video/          # 视频播放核心功能
├── 🖥️ ui/             # 用户界面组件
├── 🌐 network/        # 网络服务
├── 💾 data/           # 数据管理
├── 🛠️ utils/          # 工具类
└── 🎯 viewmodel/      # 业务逻辑
```

## 支持的Android版本

- **最低版本**: Android 7.0 (API 24)
- **目标版本**: Android 14 (API 35)
- **编译版本**: Android 14 (API 35)

## 应用权限

- `INTERNET`: 网络访问
- `ACCESS_NETWORK_STATE`: 网络状态检测
- `WAKE_LOCK`: 播放时保持屏幕常亮

## 开发亮点

1. **性能优化**
   - 播放器池管理，避免重复创建销毁
   - 智能缓存策略，减少网络请求
   - 内存泄漏防护

2. **用户体验**
   - 流畅的页面切换动画
   - 响应式布局设计
   - 直观的操作交互

3. **代码质量**
   - 清晰的模块划分
   - 完善的错误处理
   - 遵循 Android 开发最佳实践

---

**开发版本**: 1.0  
**更新时间**: 2024年 