# 播放器槽位修复 - 自测指南

## 问题描述
收藏和历史页面播放视频时出现索引错位问题：
- 点击第4个视频（index 3）：播放第2个视频（index 1）
- 点击第5个视频（index 4）：播放第4个视频（index 3）  
- 点击第6个视频（index 5）：播放第3个视频（index 2）

## 问题原因
播放器使用3个固定槽位管理播放器实例，槽位分配使用取模运算 `pageIndex % 3`，导致槽位冲突：
- 索引0,3,6... → 槽位0
- 索引1,4,7... → 槽位1  
- 索引2,5,8... → 槽位2

当槽位被复用时，如果没有正确检查pageIndex匹配，会播放错误的视频。

## 修复内容

### 1. PlayerPoolManager.kt 修复
- **getOrCreatePlayer()**: 添加pageIndex匹配检查，槽位冲突时清理并重新创建播放器
- **play()**: 确保只操作正确pageIndex的播放器
- **pause()**: 确保只操作正确pageIndex的播放器
- **updateActiveState()**: 确保只操作正确pageIndex的播放器
- **getPlayerByIndex()**: 确保只返回正确pageIndex的播放器
- **clearAllSlots()**: 新增方法，清理所有槽位避免冲突

### 2. MediaPlayerService.kt 修复
- **clearAllSlots()**: 新增方法，代理到PlayerPoolManager

### 3. VideoPlayerViewModel.kt 修复
- **initializePlaylist()**: 初始化前先清理所有槽位，避免槽位冲突

## 自测步骤

### 步骤1: 运行单元测试
```bash
./gradlew test --tests PlayerSlotTest
```
验证槽位分配逻辑和冲突检测。

### 步骤2: 准备测试数据
1. 在收藏列表中添加至少6个视频
2. 在历史记录中添加至少6个视频

### 步骤3: 测试收藏页面
1. 进入收藏列表页面
2. **测试前3个视频（index 0,1,2）**：
   - 点击第1个视频，验证播放正确
   - 点击第2个视频，验证播放正确
   - 点击第3个视频，验证播放正确

3. **测试后3个视频（index 3,4,5）**：
   - 点击第4个视频，验证播放的是第4个视频（不是第2个）
   - 点击第5个视频，验证播放的是第5个视频（不是第4个）
   - 点击第6个视频，验证播放的是第6个视频（不是第3个）

### 步骤4: 测试历史页面
重复步骤3的测试流程，在历史记录页面进行相同测试。

### 步骤5: 交叉测试
1. 先在首页播放几个视频
2. 进入收藏页面播放视频
3. 返回首页继续播放
4. 进入历史页面播放视频
5. 验证各页面播放都正确，无串扰

### 步骤6: 边界测试
1. 测试只有1-2个视频的收藏列表
2. 测试大量视频的收藏列表（10+个）
3. 测试快速切换不同视频

## 验证要点

### ✅ 修复成功的标志
- 点击第N个视频，播放的就是第N个视频
- 视频标题/ID显示正确
- 播放进度从0开始
- 收藏状态显示正确

### ❌ 仍有问题的标志
- 点击某个视频，播放的是其他视频
- 视频标题/ID与点击的不匹配
- 播放进度不是从0开始（除非有历史进度）

## 调试信息

如果问题仍然存在，可以在以下位置添加日志：

```kotlin
// PlayerPoolManager.getOrCreatePlayer()
Log.d("SlotDebug", "请求页面$pageIndex, 分配槽位$slotIndex, 槽位当前页面${existingState?.pageIndex}")

// VideoPlayerViewModel.initializePlaylist()  
Log.d("SlotDebug", "初始化播放列表, 起始索引$startIndex, 视频数量${videoList.size}")
```

## 预期结果
修复后，收藏和历史页面的视频播放应该完全准确，点击第N个视频就播放第N个视频，不再出现索引错位问题。
