# Tikoppij 开发指南

## 开发环境配置

### 必要工具

#### Android Studio
- **最低版本**: Android Studio Hedgehog (2023.1.1) 或更新版本
- **推荐版本**: Android Studio Koala (2024.1.1) 或最新稳定版

#### JDK 配置
```bash
# JDK 版本要求
JDK 11 或更高版本

# 检查当前JDK版本
java -version
javac -version
```

#### Android SDK
```bash
# 必要的SDK组件
- Android SDK Platform 35 (Android 14)
- Android SDK Build-Tools 35.0.0
- Android SDK Platform-Tools
- Android Emulator (可选，用于测试)
```

### 项目配置

#### 1. 克隆项目
```bash
git clone <repository-url>
cd tikoppij5
```

#### 2. 同步项目
```bash
# 在Android Studio中
File -> Sync Project with Gradle Files
```

#### 3. 配置local.properties
```properties
# local.properties文件配置
sdk.dir=/path/to/android/sdk
```

## 项目结构说明

### 主要目录
```
app/
├── src/main/java/com/example/tikoppij/
│   ├── data/              # 数据层 - Repository实现
│   ├── initializer/       # 应用初始化器
│   ├── model/            # 数据模型定义
│   ├── network/          # 网络服务配置
│   ├── ui/               # 用户界面层
│   │   ├── components/   # 可复用UI组件
│   │   ├── navigation/   # 导航配置
│   │   ├── screens/      # 页面级组件
│   │   └── theme/        # 主题和样式
│   ├── utils/            # 工具类和扩展函数
│   ├── video/            # 视频播放核心功能
│   ├── viewmodel/        # ViewModel层
│   ├── MainActivity.kt   # 主活动
│   └── MyApplication.kt  # 应用程序类
├── src/main/res/         # 资源文件
└── build.gradle.kts      # 模块构建配置
```

## 开发工作流

### 1. 功能开发流程

#### 创建新功能
1. **创建分支**
   ```bash
   git checkout -b feature/新功能名称
   ```

2. **定义数据模型** (如果需要)
   ```kotlin
   // 在model包下创建数据类
   data class VideoInfo(
       val id: String,
       val title: String,
       val url: String
   )
   ```

3. **实现Repository** (如果需要)
   ```kotlin
   // 在data包下创建Repository接口和实现
   interface VideoRepository {
       suspend fun getVideoList(): List<VideoInfo>
   }
   
   class VideoRepositoryImpl : VideoRepository {
       override suspend fun getVideoList(): List<VideoInfo> {
           // 实现逻辑
       }
   }
   ```

4. **创建ViewModel**
   ```kotlin
   // 在viewmodel包下
   class VideoViewModel : ViewModel() {
       private val _uiState = MutableStateFlow(VideoUiState())
       val uiState: StateFlow<VideoUiState> = _uiState.asStateFlow()
       
       // 业务逻辑方法
   }
   ```

5. **实现UI组件**
   ```kotlin
   // 在ui/screens包下
   @Composable
   fun VideoScreen(
       viewModel: VideoViewModel = viewModel()
   ) {
       val uiState by viewModel.uiState.collectAsState()
       
       // UI实现
   }
   ```

### 2. 代码审查检查点

#### 提交前检查
- [ ] 代码编译无错误
- [ ] 代码格式符合规范
- [ ] 添加必要的注释
- [ ] 单元测试通过
- [ ] UI测试正常

#### 审查要点
- 代码逻辑清晰
- 性能考虑合理
- 错误处理完善
- 遵循架构模式

## 编码规范

### Kotlin 编码规范

#### 命名规范
```kotlin
// 类名：大驼峰命名
class VideoPlayerManager

// 函数名：小驼峰命名
fun loadVideoData()

// 变量名：小驼峰命名
val videoTitle = "Sample Video"

// 常量：全大写+下划线
const val MAX_RETRY_COUNT = 3

// 私有属性：下划线前缀
private val _uiState = MutableStateFlow(...)
```

#### 函数设计原则
```kotlin
// 单一职责：一个函数只做一件事
fun loadVideoData() { /* 只负责加载数据 */ }
fun displayVideo() { /* 只负责显示视频 */ }

// 参数控制：避免过多参数
// ❌ 不推荐
fun createPlayer(url: String, autoPlay: Boolean, volume: Float, speed: Float)

// ✅ 推荐
data class PlayerConfig(
    val url: String,
    val autoPlay: Boolean = false,
    val volume: Float = 1.0f,
    val speed: Float = 1.0f
)
fun createPlayer(config: PlayerConfig)
```

### Compose 编码规范

#### 组件设计原则
```kotlin
// 状态提升：将状态提升到合适的层级
@Composable
fun VideoScreen(
    viewModel: VideoViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    VideoContent(
        videos = uiState.videos,
        onVideoClick = viewModel::onVideoClick
    )
}

@Composable
fun VideoContent(
    videos: List<VideoInfo>,
    onVideoClick: (VideoInfo) -> Unit
) {
    // 无状态组件，更容易测试和复用
}
```

#### 预览和测试
```kotlin
// 为所有组件提供预览
@Preview(showBackground = true)
@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
fun VideoContentPreview() {
    TikoppijTheme {
        VideoContent(
            videos = listOf(
                VideoInfo("1", "Sample Video", "http://example.com")
            ),
            onVideoClick = {}
        )
    }
}
```

### 资源管理规范

#### 字符串资源
```xml
<!-- strings.xml -->
<string name="app_name">Tikoppij</string>
<string name="video_loading">视频加载中…</string>
<string name="error_network">网络连接失败</string>
```

```kotlin
// 在代码中使用
Text(text = stringResource(R.string.video_loading))
```

#### 尺寸资源
```xml
<!-- dimens.xml -->
<dimen name="padding_small">8dp</dimen>
<dimen name="padding_medium">16dp</dimen>
<dimen name="padding_large">24dp</dimen>
```

## 测试指南

### 单元测试

#### ViewModel测试
```kotlin
class VideoViewModelTest {
    @Test
    fun `when loadVideo called, should update uiState`() = runTest {
        // Given
        val viewModel = VideoViewModel()
        
        // When
        viewModel.loadVideo()
        
        // Then
        assertEquals(expected, viewModel.uiState.value)
    }
}
```

#### Repository测试
```kotlin
class VideoRepositoryTest {
    @Test
    fun `when getVideoList called, should return video list`() = runTest {
        // Given
        val repository = VideoRepositoryImpl()
        
        // When
        val result = repository.getVideoList()
        
        // Then
        assertTrue(result.isNotEmpty())
    }
}
```

### UI测试

#### Compose测试
```kotlin
class VideoScreenTest {
    @get:Rule
    val composeTestRule = createComposeRule()
    
    @Test
    fun testVideoScreenDisplaysCorrectly() {
        composeTestRule.setContent {
            VideoScreen()
        }
        
        composeTestRule
            .onNodeWithText("视频标题")
            .assertIsDisplayed()
    }
}
```

## 构建和部署

### Debug构建
```bash
# 构建Debug版本
./gradlew assembleDebug

# 安装到设备
./gradlew installDebug
```

### Release构建
```bash
# 构建Release版本
./gradlew assembleRelease

# 生成签名APK
./gradlew bundleRelease
```

### 代码质量检查
```bash
# 运行Lint检查
./gradlew lint

# 运行单元测试
./gradlew test

# 运行所有检查
./gradlew check
```

## 性能优化建议

### 1. Compose性能

#### 避免不必要的重组
```kotlin
// ❌ 避免：在Composable中创建对象
@Composable
fun VideoItem() {
    val config = PlayerConfig() // 每次重组都会创建
}

// ✅ 推荐：使用remember
@Composable
fun VideoItem() {
    val config = remember { PlayerConfig() }
}
```

#### 使用稳定的参数
```kotlin
// ❌ 不稳定的参数
@Composable
fun VideoList(videos: List<VideoInfo>) { /* ... */ }

// ✅ 稳定的参数
@Composable
fun VideoList(videos: ImmutableList<VideoInfo>) { /* ... */ }
```

### 2. 内存管理

#### 及时释放资源
```kotlin
class VideoPlayerManager {
    private var player: ExoPlayer? = null
    
    fun release() {
        player?.release()
        player = null
    }
}
```

#### 避免内存泄漏
```kotlin
// 在ViewModel中
override fun onCleared() {
    super.onCleared()
    // 清理资源
    playerManager.release()
    job?.cancel()
}
```

## 常见问题解决

### 1. 构建问题

#### Gradle同步失败
```bash
# 清理并重新构建
./gradlew clean
./gradlew build
```

#### 依赖冲突
```kotlin
// 在build.gradle.kts中排除冲突的依赖
implementation("library") {
    exclude(group = "conflict.group", module = "conflict.module")
}
```

### 2. 运行时问题

#### 网络请求失败
- 检查网络权限
- 确认URL正确性
- 查看网络拦截器日志

#### 视频播放问题
- 检查播放器初始化
- 确认视频格式支持
- 查看播放器日志

## 资源链接

### 官方文档
- [Android Developers](https://developer.android.com/)
- [Jetpack Compose](https://developer.android.com/jetpack/compose)
- [Kotlin](https://kotlinlang.org/)

### 工具和库
- [Media3 ExoPlayer](https://developer.android.com/guide/topics/media/exoplayer)
- [Retrofit](https://square.github.io/retrofit/)
- [OkHttp](https://square.github.io/okhttp/)

---

遵循这个开发指南，确保代码质量和项目的可维护性！ 