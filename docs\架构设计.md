# Tikoppij 技术架构设计

## 架构概述

Tikoppij 采用现代化的 Android 应用架构，遵循 Google 推荐的最佳实践，确保代码的可维护性、可测试性和可扩展性。

## 整体架构

### MVVM + Repository 模式

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UI Layer      │    │ Domain Layer    │    │  Data Layer     │
│                 │    │                 │    │                 │
│ • Compose UI    │◄──►│ • ViewModel     │◄──►│ • Repository    │
│ • Navigation    │    │ • Use Cases     │    │ • Data Sources  │
│ • Theme         │    │ • Models        │    │ • API Service   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心架构组件

#### 1. UI Layer (用户界面层)
- **Jetpack Compose**: 声明式UI框架
- **Navigation Compose**: 页面导航管理
- **Material3**: 现代化设计系统
- **ViewModel**: UI状态管理

#### 2. Domain Layer (业务逻辑层)
- **ViewModel**: 连接UI和数据层
- **Use Cases**: 封装业务逻辑
- **Models**: 数据模型定义

#### 3. Data Layer (数据层)
- **Repository**: 数据访问抽象
- **Network**: 网络数据源
- **Local Storage**: 本地数据存储
- **Cache**: 缓存管理

## 技术栈详解

### 核心框架

#### Jetpack Compose
```kotlin
// 现代声明式UI框架
implementation "androidx.compose.ui:ui"
implementation "androidx.compose.material3:material3"
implementation "androidx.activity:activity-compose"
```

**选择理由：**
- 更少的样板代码
- 更好的性能表现
- 更强的类型安全
- 与Kotlin完美集成

#### Media3 ExoPlayer
```kotlin
// 专业的媒体播放解决方案
implementation "androidx.media3:media3-exoplayer"
implementation "androidx.media3:media3-common"
```

**优势：**
- 高度可定制的播放引擎
- 支持多种媒体格式
- 优秀的性能和稳定性
- 丰富的API和扩展性

### 网络层设计

#### Retrofit + OkHttp
```kotlin
// HTTP客户端配置
class NetworkProvider {
    private val okHttpClient = OkHttpClient.Builder()
        .addInterceptor(HttpLoggingInterceptor())
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .build()
        
    val retrofit: Retrofit = Retrofit.Builder()
        .baseUrl(BuildConfig.BASE_URL)
        .client(okHttpClient)
        .addConverterFactory(GsonConverterFactory.create())
        .build()
}
```

### 数据存储

#### DataStore Preferences
```kotlin
// 类型安全的数据存储
implementation "androidx.datastore:datastore-preferences"
```

**特点：**
- 协程友好的API
- 类型安全的访问
- 自动的数据迁移
- 更好的性能

## 模块设计

### 核心模块

#### 1. Video Module (视频模块)
```
video/
├── CacheManager.kt           # 缓存管理
├── MediaPlayerService.kt     # 播放服务
├── PlayerPoolManager.kt      # 播放器池
└── VideoIndexMapper.kt       # 视频索引
```

**职责：**
- 视频播放控制
- 播放器资源管理
- 视频缓存策略
- 播放状态监控

#### 2. UI Module (界面模块)
```
ui/
├── components/               # 可复用组件
├── screens/                  # 页面组件
├── navigation/               # 导航配置
└── theme/                   # 主题样式
```

**设计原则：**
- 组件化开发
- 单一职责原则
- 可复用性优先
- 状态提升

#### 3. Network Module (网络模块)
```
network/
└── NetworkProvider.kt        # 网络配置
```

**功能：**
- HTTP客户端配置
- 请求拦截器
- 错误处理
- 网络状态监控

### 依赖注入

#### 手动依赖注入
项目采用手动依赖注入的方式，通过单例模式和工厂模式管理依赖关系：

```kotlin
// Application级别的依赖提供
class MyApplication : Application() {
    companion object {
        lateinit var userPreferencesRepository: UserPreferencesRepository
    }
}
```

## 性能优化策略

### 1. 启动优化

#### App Startup
```kotlin
// 延迟初始化非关键组件
class AppInitializer : Initializer<Unit> {
    override fun create(context: Context) {
        // 在后台线程初始化
        initializeNetworking()
        initializeCache()
    }
}
```

#### 播放器池管理
```kotlin
class PlayerPoolManager {
    private val playerPool = mutableListOf<ExoPlayer>()
    
    fun getPlayer(): ExoPlayer {
        return playerPool.removeFirstOrNull() ?: createNewPlayer()
    }
    
    fun releasePlayer(player: ExoPlayer) {
        playerPool.add(player)
    }
}
```

### 2. 内存优化

- **播放器复用**: 避免频繁创建销毁播放器
- **图片缓存**: 智能的图片加载和缓存策略
- **组件回收**: 及时清理不需要的资源

### 3. 网络优化

- **请求合并**: 减少网络请求次数
- **智能缓存**: 本地缓存减少重复请求
- **预加载策略**: 提前加载用户可能需要的内容

## 错误处理

### 分层错误处理
```kotlin
// Repository层错误处理
sealed class Result<T> {
    data class Success<T>(val data: T) : Result<T>()
    data class Error<T>(val exception: Throwable) : Result<T>()
    data class Loading<T>(val data: T? = null) : Result<T>()
}

// ViewModel层错误处理
class VideoViewModel : ViewModel() {
    private val _uiState = MutableStateFlow(VideoUiState())
    
    fun loadVideo() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            when (val result = repository.getVideo()) {
                is Result.Success -> {
                    _uiState.value = _uiState.value.copy(
                        video = result.data,
                        isLoading = false
                    )
                }
                is Result.Error -> {
                    _uiState.value = _uiState.value.copy(
                        error = result.exception.message,
                        isLoading = false
                    )
                }
            }
        }
    }
}
```

## 测试策略

### 测试金字塔
```
┌─────────────────────┐
│    UI Tests (E2E)   │  ← 少量但关键的端到端测试
├─────────────────────┤
│ Integration Tests   │  ← 中等数量的集成测试
├─────────────────────┤
│   Unit Tests       │  ← 大量的单元测试
└─────────────────────┘
```

### 测试工具
- **JUnit**: 单元测试框架
- **Mockito**: Mock框架
- **Espresso**: UI测试
- **Compose Test**: Compose组件测试

## 代码质量保证

### 静态分析工具
- **Lint**: Android官方代码检查
- **Detekt**: Kotlin代码质量检查
- **KtLint**: Kotlin代码格式化

### 编码规范
- 遵循Kotlin官方编码规范
- 使用有意义的命名
- 合理的代码注释
- 适当的函数和类大小控制

## 构建配置

### Gradle配置
```kotlin
// 编译配置
android {
    compileSdk 35
    
    defaultConfig {
        minSdk 24
        targetSdk 35
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    
    kotlinOptions {
        jvmTarget = "11"
    }
}
```

### 版本目录管理
使用Gradle版本目录统一管理依赖版本，确保依赖的一致性和可维护性。

## 安全考虑

### 网络安全
- HTTPS强制使用
- 证书锁定
- 请求签名验证

### 数据安全
- 敏感数据加密存储
- 用户隐私保护
- 安全的数据传输

---

这个架构设计确保了Tikoppij应用的高质量、高性能和可维护性，为未来的功能扩展奠定了坚实的基础。 