package com.example.tikoppij.model

/**
 * 收藏视频数据模型
 */
data class FavoriteModel(
    val videoId: String,        // 视频ID
    val url: String,            // 视频播放链接
    val width: Int,             // 视频宽度
    val height: Int,            // 视频高度
    val category: Int,          // 分类
    val favoriteTime: Long      // 收藏时间戳
) {
    // 转换为VideoModel用于播放
    fun toVideoModel(): VideoModel {
        return VideoModel(
            url = url,
            video_id = videoId,
            Category = category,
            width = width,
            height = height
        )
    }
    
    // 获取视频宽高比
    fun getAspectRatio(): Float {
        return if (height != 0) width.toFloat() / height.toFloat() else 16f / 9f
    }
}

/**
 * 历史记录数据模型
 */
data class HistoryModel(
    val videoId: String,        // 视频ID
    val url: String,            // 视频播放链接
    val width: Int,             // 视频宽度
    val height: Int,            // 视频高度
    val category: Int,          // 分类
    val watchTime: Long,        // 观看时间戳
    val watchProgress: Long = 0 // 观看进度（毫秒），可选
) {
    // 转换为VideoModel用于播放
    fun toVideoModel(): VideoModel {
        return VideoModel(
            url = url,
            video_id = videoId,
            Category = category,
            width = width,
            height = height
        )
    }
    
    // 获取视频宽高比
    fun getAspectRatio(): Float {
        return if (height != 0) width.toFloat() / height.toFloat() else 16f / 9f
    }
} 