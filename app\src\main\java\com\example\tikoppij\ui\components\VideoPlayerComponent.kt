package com.example.tikoppij.ui.components

import android.view.TextureView
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.R
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.video.MediaPlayerService
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 视频显示模式枚举
 */
enum class VideoDisplayMode {
    AUTO_ADAPT, // 自动适应模式：横屏完整显示，竖屏填充
    FIT        // 适应模式：所有视频都完整显示，可能有黑边
}

/**
 * SurfaceView与TextureView比较:
 * - SurfaceView: 独立绘制线程和Surface，渲染效率高，占用资源少，适合纯视频播放，但不支持Z轴混合
 * - TextureView: 支持动画和变换，与UI元素更好集成，支持alpha和overlays，但占用更多资源
 * 
 * 版本: TextureView在API 14引入，两者在Media3中都得到支持
 * 
 * 选择TextureView原因: 支持UI交互和动画效果，与Compose UI集成更好，适合短视频滚动场景
 */

/**
 * 统一视频播放器组件
 * 整合了视频播放和交互功能，支持点击暂停/播放
 */
@UnstableApi
@Composable
fun VideoPlayerComponent(
    video: VideoModel,
    playerManager: MediaPlayerService,
    pageIndex: Int = -1,
    isCurrentItem: Boolean = false,
    isVisible: Boolean = false,
    displayMode: VideoDisplayMode = VideoDisplayMode.AUTO_ADAPT,
    onIsPlayingChanged: (Boolean) -> Unit,
    onLongPress: (() -> Unit)? = null
) {
    val haptic = LocalHapticFeedback.current
    // 播放器状态管理
    var isPlayerReady by remember { mutableStateOf(false) }
    // 播放状态跟踪 - 修改默认播放状态判断逻辑，与演示版本保持一致
    var isPlaying by remember { mutableStateOf(playerManager.getPlaybackState(pageIndex) != false) }
    // 控制图标显示状态
    var showControls by remember { mutableStateOf(false) }
    // 进度条状态
    var progress by remember { mutableFloatStateOf(0f) }
    // 视频总时长（毫秒）
    var duration by remember { mutableLongStateOf(0L) }
    // 进度条拖动状态
    var isDragging by remember { mutableStateOf(false) }
    
    // 控制图标大小动画 - 暂停时放大
    val iconSize by animateDpAsState(
        targetValue = if (!isPlaying) 56.dp else 48.dp,
        animationSpec = tween(300),
        label = "iconSize"
    )
    
    // 视频宽高比
    val aspectRatio = video.getAspectRatio()
    
    // 选择对应的图标
    val controlIcon: Painter = if (isPlaying) {
        painterResource(id = R.drawable.ic_pause)
    } else {
        painterResource(id = R.drawable.ic_play)
    }
    
    // 处理播放控制逻辑 - 修改为演示版本的逻辑
    fun handlePlayPauseAction() {
        if (isPlayerReady) {
            isPlaying = !isPlaying
            onIsPlayingChanged(isPlaying)
            
            // 更新播放器和存储播放状态
            if (isPlaying) {
                playerManager.play(pageIndex)
                // 点击继续播放时，延迟一小段时间后隐藏控制图标
                MainScope().launch {
                    showControls = true
                    delay(1500)
                    showControls = false
                }
            } else {
                playerManager.pause(pageIndex)
                // 点击暂停时，显示控制图标（进度条会自动显示，因为isPlaying=false）
                showControls = true
            }
            
            // 保存当前播放状态到播放器服务
            playerManager.savePlaybackState(pageIndex, isPlaying)
        }
    }
    
    // 更新视频进度
    LaunchedEffect(isCurrentItem, isVisible, isPlaying, isPlayerReady) {
        if (isCurrentItem && isVisible && isPlayerReady) {
            // 获取视频时长
            val currentPlayer = playerManager.getPlayerByIndex(pageIndex)
            currentPlayer?.let {
                if (duration <= 0 && it.duration > 0) {
                    duration = it.duration
                }
            }
            
            // 持续更新进度
            while (true) {
                if (isPlaying && !isDragging) {  // 仅当播放且非拖动时更新进度
                    val progressPlayer = playerManager.getPlayerByIndex(pageIndex)
                    progressPlayer?.let {
                        if (duration > 0) {
                            progress = (it.currentPosition.toFloat() / duration).coerceIn(0f, 1f)
                        }
                    }
                }
                delay(100) // 每100ms更新一次，更加流畅
            }
        }
    }
    
    // 自动隐藏控制图标，但当暂停时保持显示进度条
    LaunchedEffect(showControls) {
        if (showControls) {
            delay(2000)
            showControls = false
        }
    }
    
    // 添加滑动回来的专门处理逻辑
    LaunchedEffect(isCurrentItem, isVisible) {
        if (isCurrentItem && isVisible && isPlayerReady) {
            val player = playerManager.getPlayerByIndex(pageIndex)
            
            // 当滑回视频时继续播放
            isPlaying = true
            onIsPlayingChanged(true)
            playerManager.savePlaybackState(pageIndex, true)
            
            // 隐藏控制UI
            isDragging = false
            delay(300)
            showControls = false

            // 处理视频播放完毕的情况
            player?.let { exoPlayer ->
                if (exoPlayer.playbackState == Player.STATE_ENDED) {
                    exoPlayer.seekTo(0)
                }
                exoPlayer.play()
            }
            
            playerManager.play(pageIndex)
        }
    }
    
    // 主容器：包含视频和所有覆盖控件
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
            .pointerInput(Unit) { // Keyed on Unit, or pageIndex if behavior within callbacks changes
                detectTapGestures(
                    onTap = { handlePlayPauseAction() },
                    onLongPress = {
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        onLongPress?.invoke()
                    }
                )
            }
    ) {
        // 1. 视频表面视图
        AndroidView(
            modifier = Modifier
                .fillMaxSize()
                // 根据不同的显示模式处理视频
                .run {
                    when (displayMode) {
                        VideoDisplayMode.AUTO_ADAPT -> {
                            // 定义一个临界宽高比阈值。
                            // 宽高比大于此阈值的视频（包括横屏、方形、不够窄的竖屏如3:4）将适应宽度、不裁剪。
                            // 宽高比小于等于此阈值的视频（更窄的竖屏如9:16）将适应高度、裁剪左右。
                            val thresholdRatioForFillingHeight = 0.7f 

                            if (aspectRatio > thresholdRatioForFillingHeight) {
                                // 适应宽度显示，上下黑边，不裁剪内容 (matchHeightConstraintsFirst = false)
                                aspectRatio(aspectRatio, matchHeightConstraintsFirst = false)
                            } else {
                                // 适应高度显示，左右可能裁剪 (matchHeightConstraintsFirst = true)
                                aspectRatio(aspectRatio, matchHeightConstraintsFirst = true)
                            }
                        }
                        VideoDisplayMode.FIT -> {
                            // FIT 模式：所有视频都完整显示，优先适应宽度，上下或左右加黑边，不裁剪内容
                            aspectRatio(aspectRatio, matchHeightConstraintsFirst = false)
                        }
                    }
                }
            ,
            factory = { ctx ->
                TextureView(ctx).apply {
                    layoutParams = android.widget.FrameLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT)

                    android.util.Log.d("VideoComponent", "创建TextureView，页面$pageIndex，视频${video.video_id}")

                    val surfacePlayer = playerManager.getOrCreatePlayer(video, pageIndex)
                    surfacePlayer.setVideoTextureView(this)

                    // 如果之前是暂停状态，确保视频不会自动播放
                    if (!isPlaying) {
                        surfacePlayer.pause()
                    }

                    // 获取视频时长
                    surfacePlayer.addListener(object : Player.Listener {
                        override fun onPlaybackStateChanged(state: Int) {
                            if (state == Player.STATE_READY) {
                                duration = surfacePlayer.duration
                                android.util.Log.d("VideoComponent", "播放器就绪，页面$pageIndex")
                            }
                        }
                    })

                    isPlayerReady = true
                }
            },
            update = { textureView ->
                // 获取当前播放器
                val currentPlayer = playerManager.getPlayerByIndex(pageIndex)

                if (currentPlayer != null) {
                    // 如果播放器存在，检查是否需要更新视频内容
                    if (currentPlayer.currentMediaItem?.mediaId != video.video_id) {
                        android.util.Log.d("VideoComponent", "更新播放器内容，页面$pageIndex，新视频${video.video_id}")
                        // 更新播放器内容而不是重新创建
                        currentPlayer.stop()
                        currentPlayer.clearMediaItems()
                        currentPlayer.setMediaItem(MediaItem.Builder()
                            .setUri(video.url)
                            .setMediaId(video.video_id)
                            .build())
                        currentPlayer.prepare()
                        currentPlayer.setVideoTextureView(textureView)
                    } else {
                        // 视频内容相同，只需要重新绑定TextureView
                        android.util.Log.d("VideoComponent", "重新绑定TextureView，页面$pageIndex")
                        currentPlayer.setVideoTextureView(textureView)
                    }
                } else {
                    // 播放器不存在，创建新的
                    android.util.Log.d("VideoComponent", "播放器不存在，重新创建，页面$pageIndex")
                    val newPlayer = playerManager.getOrCreatePlayer(video, pageIndex)
                    newPlayer.setVideoTextureView(textureView)
                }
            }
        )
        
        // 2. 播放/暂停控制图标 - 中心位置（覆盖在视频上）
        if (showControls && isVisible) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = controlIcon,
                    contentDescription = if (isPlaying) stringResource(id = R.string.pause_video) else stringResource(id = R.string.play_video),
                    tint = Color.White,
                    modifier = Modifier.size(iconSize)
                )
            }
        }
        
        // 3. 进度条 - 底部位置（覆盖在视频上，仅在暂停或拖动时显示）
        if ((!isPlaying || isDragging) && isVisible) {
            val trackHeight = 4.dp
            val thumbSize = 12.dp
            val progressBarTouchableHeight = thumbSize + 8.dp 

            BoxWithConstraints(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 16.dp)
                    .fillMaxWidth(0.95f) 
                    .height(progressBarTouchableHeight)
                    .pointerInput(Unit) { // Tap gestures on the BoxWithConstraints
                        detectTapGestures {
                            if (isPlayerReady) {
                                val player = playerManager.getPlayerByIndex(pageIndex)
                                player?.let { exoPlayer ->
                                    val totalDuration = exoPlayer.duration
                                    if (totalDuration <= 0) return@let

                                    // size.width here refers to the width of BoxWithConstraints
                                    val newProgress = (it.x / size.width).coerceIn(0f, 1f)
                                    progress = newProgress
                                    exoPlayer.seekTo((totalDuration * newProgress).toLong())

                                    if (!isPlaying) {
                                        isPlaying = true
                                        onIsPlayingChanged(true)
                                        exoPlayer.play()
                                    }
                                    playerManager.savePlaybackState(pageIndex, true)
                                }
                            }
                        }
                    }
                    .pointerInput(Unit) { // Drag gestures on the BoxWithConstraints
                        detectDragGestures(
                            onDragStart = { 
                                isDragging = true 
                                val player = playerManager.getPlayerByIndex(pageIndex)
                                player?.let {
                                    if (isPlaying) {
                                        it.pause() 
                                    }
                                }
                            },
                            onDragEnd = {
                                isDragging = false
                                val player = playerManager.getPlayerByIndex(pageIndex)
                                player?.let {
                                    isPlaying = true
                                    onIsPlayingChanged(true)
                                    it.play()
                                    playerManager.savePlaybackState(pageIndex, true)
                                }
                            },
                            onDragCancel = {
                                isDragging = false
                                val player = playerManager.getPlayerByIndex(pageIndex)
                                player?.let {
                                    isPlaying = true
                                    onIsPlayingChanged(true)
                                    it.play()
                                    playerManager.savePlaybackState(pageIndex, true)
                                }
                            },
                            onDrag = { change, _ -> 
                                if (isPlayerReady) {
                                    val player = playerManager.getPlayerByIndex(pageIndex)
                                    player?.let { exoPlayer ->
                                        val totalDuration = exoPlayer.duration
                                        if (totalDuration <= 0) return@let
                                        // size.width here refers to the width of BoxWithConstraints
                                        val newProgress = (change.position.x / size.width).coerceIn(0f, 1f)
                                        progress = newProgress
                                        exoPlayer.seekTo((totalDuration * newProgress).toLong())
                                    }
                                }
                            }
                        )
                    }
            ) {
                // 使用 BoxWithConstraintsScope 直接提供的 maxWidth (Dp)，然后转换为 Px
                val maxWidthInDp = this.maxWidth // 'this' here is BoxWithConstraintsScope
                val bwcWidthPx = LocalDensity.current.run { maxWidthInDp.toPx() }
                val thumbSizePx = LocalDensity.current.run { thumbSize.toPx() }

                // Background Track
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(trackHeight)
                        .background(Color.DarkGray.copy(alpha = 0.6f), RoundedCornerShape(trackHeight / 2))
                        .align(Alignment.Center) // Center the track vertically within progressBarTouchableHeight
                )
                // Progress Track
                Box(
                    modifier = Modifier
                        .fillMaxWidth(progress)
                        .height(trackHeight)
                        .background(Color.White, RoundedCornerShape(trackHeight / 2))
                        .align(Alignment.CenterStart) // Align to start, and will be centered vertically by parent's alignment of track
                )
                // Thumb (Indicator)
                Box(
                    modifier = Modifier
                        .align(Alignment.CenterStart) // Align thumb to the vertical center and horizontal start of BoxWithConstraints
                        .size(thumbSize)
                        .graphicsLayer {
                            translationX = (bwcWidthPx * progress) - (thumbSizePx / 2)
                        }
                        .background(Color.White, CircleShape)
                ) 
            }
        }
        
        // 显示模式现在由VideoScreen统一管理
    }
    
    // 自动管理播放状态
    DisposableEffect(isCurrentItem, isVisible) {
        if (isCurrentItem && isVisible && isPlayerReady) {
            playerManager.play(pageIndex)
        } else if (!isVisible && isPlayerReady) {
            playerManager.pause(pageIndex, updateActiveStatus = false)
        }
        
        onDispose {
            if (isPlayerReady) {
                playerManager.pause(pageIndex, updateActiveStatus = false)
            }
        }
    }
} 