package com.example.tikoppij.video

import org.junit.Test
import org.junit.Assert.*

/**
 * 播放器槽位测试
 * 用于验证槽位分配逻辑的正确性
 */
class PlayerSlotTest {

    /**
     * 测试槽位分配逻辑
     */
    @Test
    fun testSlotAllocation() {
        val playerSlotCount = 3
        
        // 模拟getSlotIndexForPage方法
        fun getSlotIndexForPage(pageIndex: Int): Int {
            return pageIndex.coerceAtLeast(0) % playerSlotCount
        }
        
        // 测试前6个索引的槽位分配
        val expectedSlots = mapOf(
            0 to 0,  // 索引0 -> 槽位0
            1 to 1,  // 索引1 -> 槽位1
            2 to 2,  // 索引2 -> 槽位2
            3 to 0,  // 索引3 -> 槽位0 (冲突!)
            4 to 1,  // 索引4 -> 槽位1 (冲突!)
            5 to 2   // 索引5 -> 槽位2 (冲突!)
        )
        
        println("=== 槽位分配测试 ===")
        expectedSlots.forEach { (pageIndex, expectedSlot) ->
            val actualSlot = getSlotIndexForPage(pageIndex)
            assertEquals("页面索引 $pageIndex 应该分配到槽位 $expectedSlot", expectedSlot, actualSlot)
            println("页面索引 $pageIndex -> 槽位 $actualSlot")
        }
        
        // 验证冲突情况
        println("\n=== 槽位冲突分析 ===")
        println("索引3和索引0都使用槽位0 - 这会导致播放错误!")
        println("索引4和索引1都使用槽位1 - 这会导致播放错误!")
        println("索引5和索引2都使用槽位2 - 这会导致播放错误!")
    }
    
    /**
     * 测试用户报告的具体问题场景
     */
    @Test
    fun testUserReportedIssue() {
        println("\n=== 用户报告问题验证 ===")
        
        // 用户报告的问题：
        // 点击第4个视频（index 3）：播放第2个视频（index 1）
        // 点击第5个视频（index 4）：播放第4个视频（index 3）
        // 点击第6个视频（index 5）：播放第3个视频（index 2）
        
        val playerSlotCount = 3
        fun getSlotIndexForPage(pageIndex: Int): Int {
            return pageIndex.coerceAtLeast(0) % playerSlotCount
        }
        
        // 模拟槽位状态
        data class SlotState(var pageIndex: Int, var videoId: String)
        val slots = arrayOfNulls<SlotState>(playerSlotCount)
        
        // 初始化前3个视频
        for (i in 0..2) {
            val slotIndex = getSlotIndexForPage(i)
            slots[slotIndex] = SlotState(i, "video_$i")
            println("初始化: 视频$i -> 槽位$slotIndex")
        }
        
        println("\n当前槽位状态:")
        slots.forEachIndexed { index, state ->
            println("槽位$index: ${state?.let { "页面${it.pageIndex}, 视频${it.videoId}" } ?: "空"}")
        }
        
        // 测试点击第4个视频（index 3）
        println("\n点击第4个视频（index 3）:")
        val slot3 = getSlotIndexForPage(3)
        println("分配到槽位$slot3")
        val existingState3 = slots[slot3]
        if (existingState3 != null) {
            println("槽位$slot3 已被页面${existingState3.pageIndex}占用")
            println("如果没有正确检查pageIndex匹配，会播放错误的视频!")
        }
        
        // 测试点击第5个视频（index 4）
        println("\n点击第5个视频（index 4）:")
        val slot4 = getSlotIndexForPage(4)
        println("分配到槽位$slot4")
        val existingState4 = slots[slot4]
        if (existingState4 != null) {
            println("槽位$slot4 已被页面${existingState4.pageIndex}占用")
            println("如果没有正确检查pageIndex匹配，会播放错误的视频!")
        }
        
        // 测试点击第6个视频（index 5）
        println("\n点击第6个视频（index 5）:")
        val slot5 = getSlotIndexForPage(5)
        println("分配到槽位$slot5")
        val existingState5 = slots[slot5]
        if (existingState5 != null) {
            println("槽位$slot5 已被页面${existingState5.pageIndex}占用")
            println("如果没有正确检查pageIndex匹配，会播放错误的视频!")
        }
    }
}
