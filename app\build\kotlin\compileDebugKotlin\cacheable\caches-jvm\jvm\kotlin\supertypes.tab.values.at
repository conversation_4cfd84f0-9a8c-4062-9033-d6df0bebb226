/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity android.app.Application- ,com.example.tikoppij.data.FavoriteRepository, +com.example.tikoppij.data.HistoryRepository4 3com.example.tikoppij.data.UserPreferencesRepository androidx.startup.Initializer kotlin.Enum3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel- ,com.example.tikoppij.data.FavoriteRepository, +com.example.tikoppij.data.HistoryRepository4 3com.example.tikoppij.data.UserPreferencesRepository3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations3 2com.example.tikoppij.ui.navigation.AppDestinations$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel kotlin.Enum kotlin.Enum