package com.example.tikoppij.video

import androidx.media3.common.util.UnstableApi
import java.util.concurrent.ConcurrentHashMap

/**
 * 视频索引映射器
 * 负责维护视频ID和页面索引之间的映射关系
 */
@UnstableApi
class VideoIndexMapper {
    
    // 视频ID到索引的映射
    private val videoIdToIndex = ConcurrentHashMap<String, Int>()
    
    // 索引到视频ID的映射
    private val indexToVideoId = ConcurrentHashMap<Int, String>()
    
    /**
     * 更新单个视频ID和索引的映射
     */
    fun updateMapping(videoId: String, index: Int) {
        if (index < 0) return
        synchronized(this) {
            videoIdToIndex[videoId] = index
            indexToVideoId[index] = videoId
        }
    }
    
    /**
     * 批量更新视频ID和索引的映射
     */
    fun updateMappingBatch(startIndex: Int, videoIds: List<String>) {
        val mappings = videoIds.mapIndexed { offset, videoId ->
            val index = startIndex + offset
            Pair(videoId, index)
        }
        synchronized(this) {
            mappings.forEach { (videoId, index) ->
                if (index >= 0) {
                    videoIdToIndex[videoId] = index
                    indexToVideoId[index] = videoId
                }
            }
        }
    }

}