package com.example.tikoppij.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.example.tikoppij.R

/**
 * 通用视频控制按钮组件
 * 用于主页、收藏页面、播放页面的右下角功能按钮
 */
@Composable
fun VideoControlButtons(
    modifier: Modifier = Modifier,
    isFavorite: Boolean = false,
    onFavoriteClick: () -> Unit = {},
    onDownloadClick: () -> Unit = {},
    onShareClick: () -> Unit = {},
    onMenuClick: (() -> Unit)? = null,
    onToggleBottomBarVisibility: (() -> Unit)? = null,
    isBottomBarVisible: Boolean = true,
    showToggleButton: Boolean = true,
    showMenuButton: Boolean = true
) {
    Column(
        modifier = modifier
    ) {
        // 收藏按钮
        VideoControlButton(
            onClick = onFavoriteClick
        ) {
            Icon(
                painter = painterResource(id = if (isFavorite) R.drawable.ic_favorite else R.drawable.ic_favorite_border),
                contentDescription = null,
                tint = if (isFavorite) Color.Red else Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 下载按钮
        VideoControlButton(
            onClick = onDownloadClick
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_download),
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 分享按钮
        VideoControlButton(
            onClick = onShareClick
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_share),
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
        
        // 隐藏/显示底部导航栏按钮（仅在主页显示）
        if (showToggleButton && onToggleBottomBarVisibility != null) {
            Spacer(modifier = Modifier.height(12.dp))
            
            VideoControlButton(
                onClick = onToggleBottomBarVisibility
            ) {
                Icon(
                    painter = painterResource(id = if (isBottomBarVisible) R.drawable.ic_visibility_off else R.drawable.ic_visibility),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        // 菜单按钮
        if (showMenuButton && onMenuClick != null) {
            Spacer(modifier = Modifier.height(12.dp))
            
            VideoControlButton(
                onClick = onMenuClick
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_menu),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}

/**
 * 单个视频控制按钮
 */
@Composable
private fun VideoControlButton(
    onClick: () -> Unit,
    content: @Composable () -> Unit
) {
    Box(
        modifier = Modifier
            .size(48.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(Color.Black.copy(alpha = 0.5f))
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        content()
    }
} 